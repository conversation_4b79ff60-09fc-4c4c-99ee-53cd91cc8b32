-- Migration to add financial fields to oa_ofertas_academicas table
-- These fields are required for Etapa 3 (Aranceles) functionality

-- Add financial fields to the oa_ofertas_academicas table
ALTER TABLE oferta_academica.oa_ofertas_academicas 
ADD COLUMN formato_valor INTEGER,
ADD COLUMN valor_matricula_anual DECIMAL(15,2),
ADD COLUMN costo_titulacion DECIMAL(15,2),
ADD COLUMN valor_certificado_diploma DECIMAL(15,2),
ADD COLUMN arancel_anual DECIMAL(15,2);

-- Add constraints for formato_valor (1 = Pesos, 2 = UF)
ALTER TABLE oferta_academica.oa_ofertas_academicas 
ADD CONSTRAINT chk_formato_valor CHECK (formato_valor IN (1, 2));

-- Add constraints for financial values (must be >= 0)
ALTER TABLE oferta_academica.oa_ofertas_academicas 
ADD CONSTRAINT chk_valor_matricula_anual CHECK (valor_matricula_anual >= 0);

ALTER TABLE oferta_academica.oa_ofertas_academicas 
ADD CONSTRAINT chk_costo_titulacion CHECK (costo_titulacion >= 0);

ALTER TABLE oferta_academica.oa_ofertas_academicas 
ADD CONSTRAINT chk_valor_certificado_diploma CHECK (valor_certificado_diploma >= 0);

ALTER TABLE oferta_academica.oa_ofertas_academicas 
ADD CONSTRAINT chk_arancel_anual CHECK (arancel_anual >= 0);

-- Add comments to document the fields
COMMENT ON COLUMN oferta_academica.oa_ofertas_academicas.formato_valor IS 'Format of financial values: 1=Pesos, 2=UF';
COMMENT ON COLUMN oferta_academica.oa_ofertas_academicas.valor_matricula_anual IS 'Annual enrollment fee';
COMMENT ON COLUMN oferta_academica.oa_ofertas_academicas.costo_titulacion IS 'Cost of degree certification';
COMMENT ON COLUMN oferta_academica.oa_ofertas_academicas.valor_certificado_diploma IS 'Cost of certificate/diploma';
COMMENT ON COLUMN oferta_academica.oa_ofertas_academicas.arancel_anual IS 'Annual tuition fee';
