import { Op } from "sequelize";
import OAOfertaAcademica from "../../models/Oferta-<PERSON><PERSON>-<PERSON>/O<PERSON>OfertaAcademica.js";
import OAEtapa from "../../models/Oferta-Academic<PERSON>-<PERSON>/OAEtapa.js";
import OASubProceso from "../../models/Oferta-<PERSON><PERSON>-<PERSON>/OASubProceso.js";
import OAProceso from "../../models/Oferta-<PERSON><PERSON>-<PERSON>/OAProceso.js";
import { checkTokenLocal, getEmailFromToken } from "../../auth/auth.js";
import Usuario from "../../models/Usuario/usuario.js";
import { sequelize } from "../../database/database.js";
import { checkAtributosNecesarios } from "../../utils/checkAtributosNecesarios.js";
import { Sequelize } from "sequelize";
import iconv from "iconv-lite";

/**
 * Helper function to check database connection
 */
const checkDatabase = async () => {
  try {
    console.log("Checking database connection...");
    await sequelize.authenticate();
    console.log("Database is connected successfully.");
  } catch (error) {
    console.error("Database connection error:", error);
    throw new Error("Database not connected");
  }
};

/**
 * Get all OAOfertaAcademicas
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllOAOfertaAcademicas = async (req, res) => {
  try {
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find all ofertas academicas
    const ofertasAcademicas = await OAOfertaAcademica.findAll({
      include: [
        {
          model: OAEtapa,
          attributes: [
            "id",
            "subproceso_id",
            "tipo",
            "etapa",
            "fecha_inicial",
            "fecha_final",
          ],
          include: [
            {
              model: OASubProceso,
              attributes: ["id", "proceso_id", "tipo"],
            },
          ],
        },
      ],
    });

    res.status(200).json({
      message: "Ofertas Academicas retrieved successfully",
      data: ofertasAcademicas,
    });
  } catch (error) {
    console.error("Error retrieving Ofertas Academicas:", error);
    res.status(500).json({
      message: "Error retrieving Ofertas Academicas",
      error: error.message,
    });
  }
};

/**
 * Get OAOfertaAcademica by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOAOfertaAcademicaById = async (req, res) => {
  try {
    const { id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find oferta academica by ID
    const ofertaAcademica = await OAOfertaAcademica.findByPk(id, {
      include: [
        {
          model: OAEtapa,
          attributes: [
            "id",
            "subproceso_id",
            "tipo",
            "etapa",
            "fecha_inicial",
            "fecha_final",
          ],
          include: [
            {
              model: OASubProceso,
              attributes: ["id", "proceso_id", "tipo"],
            },
          ],
        },
      ],
    });

    if (!ofertaAcademica) {
      return res.status(404).json({ message: "Oferta Academica not found" });
    }

    res.status(200).json({
      message: "Oferta Academica retrieved successfully",
      data: ofertaAcademica,
    });
  } catch (error) {
    console.error("Error retrieving Oferta Academica:", error);
    res.status(500).json({
      message: "Error retrieving Oferta Academica",
      error: error.message,
    });
  }
};

/**
 * Get OAOfertaAcademicas by Etapa ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOAOfertaAcademicasByEtapaId = async (req, res) => {
  try {
    const { etapa_id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find ofertas academicas by etapa_id
    const ofertasAcademicas = await OAOfertaAcademica.findAll({
      where: { etapa_id },
      include: [
        {
          model: OAEtapa,
          attributes: [
            "id",
            "subproceso_id",
            "tipo",
            "etapa",
            "fecha_inicial",
            "fecha_final",
          ],
          include: [
            {
              model: OASubProceso,
              attributes: ["id", "proceso_id", "tipo"],
            },
          ],
        },
      ],
    });

    res.status(200).json({
      message: "Ofertas Academicas retrieved successfully",
      data: ofertasAcademicas,
    });
  } catch (error) {
    console.error("Error retrieving Ofertas Academicas:", error);
    res.status(500).json({
      message: "Error retrieving Ofertas Academicas",
      error: error.message,
    });
  }
};

/**
 * Get OAOfertaAcademicas by Etapa ID and return as CSV-ready data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOAOfertaAcademicasByEtapaIdForCSV = async (req, res) => {
  const userToken = req.headers.authorization;
  const { etapa_id } = req.params;

  try {
    // Check the database connection
    await checkDatabase();

    // Check if the token is valid and not expired
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: `Token is not valid`,
      });
    }

    // First, get the etapa information to determine the etapa number
    const etapa = await OAEtapa.findByPk(etapa_id);
    if (!etapa) {
      return res.status(404).json({ message: "Etapa not found" });
    }

    // Fetch data from the database based on etapa_id
    const ofertasAcademicas = await OAOfertaAcademica.findAll({
      where: { etapa_id },
    });

    if (!ofertasAcademicas || ofertasAcademicas.length === 0) {
      return res.status(404).json({ message: "Data not found" });
    }

    // Get etapa-specific columns instead of all database columns
    const headers = getEtapaColumns(etapa.etapa);

    // Prepare the data for CSV, filtering only the relevant columns
    const csvData = ofertasAcademicas.map((item) => {
      const filteredData = {};
      headers.forEach(header => {
        filteredData[header] = item.dataValues[header];
      });
      return filteredData;
    });

    // Create CSV content
    let csvContent =
      headers.map((header) => header.toUpperCase()).join(";") + "\n";
    csvContent += csvData
      .map((row) => headers.map((header) => {
        const value = row[header];
        // Handle null/undefined as empty, but preserve 0 values
        return (value === null || value === undefined) ? "" : value;
      }).join(";"))
      .join("\n");

    // Send the CSV to the front end
    const csvBuffer = iconv.encode(csvContent, "windows-1252");
    res.setHeader("Content-Type", "text/csv; charset=windows-1252");
    res.setHeader("Content-Disposition", 'attachment; filename="data.csv"');
    res.setHeader("Content-Length", csvBuffer.length);
    res.send(csvBuffer);
  } catch (error) {
    console.error("Error retrieving Ofertas Academicas:", error);
    res.status(500).json({
      message: "Error retrieving Ofertas Academicas",
      error: error.message,
    });
  }
};

/**
 * Create a new OAOfertaAcademica
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createOAOfertaAcademica = async (req, res) => {
  try {
    const {
      etapa_id,
      cod_sede,
      nombre_sede,
      cod_carrera,
      nombre_carrera,
      modalidad,
      cod_jornada,
      version,
      cod_tipo_plan_carrera,
      caracteristicas_tipo_plan,
      duracion_estudios,
      duracion_titulacion,
      duracion_total,
      regimen,
      duracion_regimen,
      nombre_titulo,
      nombre_grado,
      cod_nivel_global,
      cod_nivel_carrera,
      cod_demre,
      anio_inicio,
      acreditacion,
      elegible_beca_pedagogia,
      ped_med_odont_otro,
      requisito_ingreso,
      semestres_reconocidos,
      area_actual,
      area_destino_agricultura,
      area_destino_ciencias,
      area_destino_cs_sociales,
      area_destino_educacion,
      area_destino_humanidades,
      area_destino_ingenieria,
      area_destino_salud,
      area_destino_servicios,
      ponderacion_nem,
      ponderacion_ranking,
      ponderacion_c_lectora,
      ponderacion_matematicas,
      ponderacion_matematicas_2,
      ponderacion_historia,
      ponderacion_ciencias,
      ponderacion_otros,
      vacantes_primer_semestre,
      vacantes_segundo_semestre,
      vacantes_pace,
      malla_curricular,
      perfil_egreso,
      texto_requisito_ingreso,
      otros_requisitos,
      mail_difusion_carrera,
      vigencia_carrera,
    } = req.body;

    const userToken = req.headers.authorization;

    // Required fields
    const requiredFields = [
      "etapa_id",
      "cod_sede",
      "nombre_sede",
      // "cod_carrera",
      "nombre_carrera",
      "modalidad",
      "cod_jornada",
      "version",
      "cod_tipo_plan_carrera",
      "caracteristicas_tipo_plan",
      "duracion_estudios",
      "duracion_titulacion",
      "duracion_total",
      "regimen",
      "duracion_regimen",
      "nombre_titulo",
      "nombre_grado",
      "cod_nivel_global",
      "cod_nivel_carrera",
      "cod_demre",
      "anio_inicio",
      "acreditacion",
      "elegible_beca_pedagogia",
      "ped_med_odont_otro",
      "requisito_ingreso",
      "semestres_reconocidos",
      "area_actual",
      "ponderacion_nem",
      "ponderacion_ranking",
      "ponderacion_c_lectora",
      "ponderacion_matematicas",
      "ponderacion_matematicas_2",
      "ponderacion_historia",
      "ponderacion_ciencias",
      "ponderacion_otros",
      "vacantes_primer_semestre",
      "vacantes_segundo_semestre",
      "vacantes_pace",
      "malla_curricular",
      // "perfil_egreso", // Already optional
      // "texto_requisito_ingreso", // Already optional
      // "otros_requisitos", // Already optional
      // "mail_difusion_carrera",
      "vigencia_carrera",
    ];

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Check for missing required attributes
    const missingAttributes = checkAtributosNecesarios(
      req.body,
      requiredFields
    );

    if (missingAttributes.length > 0) {
      return res.status(400).json({
        message: "Faltan atributos requeridos",
        missingAttributes,
      });
    }

    // Check if etapa exists
    const etapa = await OAEtapa.findByPk(etapa_id);
    if (!etapa) {
      return res.status(404).json({
        message: "Etapa not found",
      });
    }

    // Validate text fields with specific character restrictions
    const validationErrors = [];

    if (perfil_egreso) {
      const invalidChars = findInvalidChars(
        perfil_egreso,
        /[^A-Za-zÀ-ÖØ-öø-ÿ0-9\s.,;:()[\]{}'"¡!¿?@#$%&*+-_=<>]/g
      );
      if (invalidChars.length > 0) {
        validationErrors.push(
          `perfil_egreso contiene caracteres no válidos: "${invalidChars.join(
            '", "'
          )}". Please check for special characters that are not allowed.`
        );
      }
    }

    if (texto_requisito_ingreso) {
      const invalidChars = findInvalidChars(
        texto_requisito_ingreso,
        /[^A-Za-zÀ-ÖØ-öø-ÿ0-9\s.,;:()[\]{}'"¡!¿?@#$%&*+-_=<>]/g
      );
      if (invalidChars.length > 0) {
        validationErrors.push(
          `texto_requisito_ingreso contiene caracteres no válidos: "${invalidChars.join(
            '", "'
          )}". Please check for special characters that are not allowed.`
        );
      }
    }

    if (otros_requisitos) {
      const invalidChars = findInvalidChars(
        otros_requisitos,
        /[^A-Za-zÀ-ÖØ-öø-ÿ0-9\s.,;:()[\]{}'"¡!¿?@#$%&*+-_=<>-]/g
      );
      if (invalidChars.length > 0) {
        validationErrors.push(
          `otros_requisitos contiene caracteres no válidos: "${invalidChars.join(
            '", "'
          )}". Please check for special characters that are not allowed.`
        );
      }
    }

    // If validation errors exist, return them
    if (validationErrors.length > 0) {
      return res.status(400).json({
        message: "Validation errors in oferta academica data",
        errors: validationErrors,
      });
    }

    // Create new oferta academica
    const newOfertaAcademica = await OAOfertaAcademica.create({
      etapa_id,
      cod_sede,
      nombre_sede,
      cod_carrera,
      nombre_carrera,
      modalidad,
      cod_jornada,
      version,
      cod_tipo_plan_carrera,
      caracteristicas_tipo_plan,
      duracion_estudios,
      duracion_titulacion,
      duracion_total,
      regimen,
      duracion_regimen,
      nombre_titulo,
      nombre_grado,
      cod_nivel_global,
      cod_nivel_carrera,
      cod_demre,
      anio_inicio,
      acreditacion,
      elegible_beca_pedagogia,
      ped_med_odont_otro,
      requisito_ingreso,
      semestres_reconocidos,
      area_actual,
      area_destino_agricultura,
      area_destino_ciencias,
      area_destino_cs_sociales,
      area_destino_educacion,
      area_destino_humanidades,
      area_destino_ingenieria,
      area_destino_salud,
      area_destino_servicios,
      ponderacion_nem,
      ponderacion_ranking,
      ponderacion_c_lectora,
      ponderacion_matematicas,
      ponderacion_matematicas_2,
      ponderacion_historia,
      ponderacion_ciencias,
      ponderacion_otros,
      vacantes_primer_semestre,
      vacantes_segundo_semestre,
      vacantes_pace,
      malla_curricular,
      perfil_egreso,
      texto_requisito_ingreso,
      otros_requisitos,
      mail_difusion_carrera,
      vigencia_carrera,
    });

    res.status(201).json({
      message: "Oferta Academica created successfully",
      data: newOfertaAcademica,
    });
  } catch (error) {
    console.error("Error creating Oferta Academica:", error);
    res.status(500).json({
      message: "Error creating Oferta Academica",
      error: error.message,
    });
  }
};

/**
 * Update an existing OAOfertaAcademica
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateOAOfertaAcademica = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      etapa_id,
      cod_sede,
      nombre_sede,
      cod_carrera,
      nombre_carrera,
      modalidad,
      cod_jornada,
      version,
      cod_tipo_plan_carrera,
      caracteristicas_tipo_plan,
      duracion_estudios,
      duracion_titulacion,
      duracion_total,
      regimen,
      duracion_regimen,
      nombre_titulo,
      nombre_grado,
      cod_nivel_global,
      cod_nivel_carrera,
      cod_demre,
      anio_inicio,
      acreditacion,
      elegible_beca_pedagogia,
      ped_med_odont_otro,
      requisito_ingreso,
      semestres_reconocidos,
      area_actual,
      area_destino_agricultura,
      area_destino_ciencias,
      area_destino_cs_sociales,
      area_destino_educacion,
      area_destino_humanidades,
      area_destino_ingenieria,
      area_destino_salud,
      area_destino_servicios,
      ponderacion_nem,
      ponderacion_ranking,
      ponderacion_c_lectora,
      ponderacion_matematicas,
      ponderacion_matematicas_2,
      ponderacion_historia,
      ponderacion_ciencias,
      ponderacion_otros,
      vacantes_primer_semestre,
      vacantes_segundo_semestre,
      vacantes_pace,
      malla_curricular,
      perfil_egreso,
      texto_requisito_ingreso,
      otros_requisitos,
      mail_difusion_carrera,
      vigencia_carrera,
      // Financial fields for Etapa 3
      formato_valor,
      valor_matricula_anual,
      costo_titulacion,
      valor_certificado_diploma,
      arancel_anual,
    } = req.body;

    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find oferta academica by ID
    const ofertaAcademicaToUpdate = await OAOfertaAcademica.findByPk(id);

    if (!ofertaAcademicaToUpdate) {
      return res.status(404).json({ message: "Oferta Academica not found" });
    }

    // If etapa_id is provided, check if etapa exists
    if (etapa_id) {
      const etapa = await OAEtapa.findByPk(etapa_id);
      if (!etapa) {
        return res.status(404).json({
          message: "Etapa not found",
        });
      }
    }

    // Validate text fields with specific character restrictions
    const validationErrors = [];

    if (perfil_egreso) {
      const invalidChars = findInvalidChars(
        perfil_egreso,
        /[^A-Za-zÀ-ÖØ-öø-ÿ0-9\s.,;:()[\]{}'"¡!¿?@#$%&*+-_=<>]/g
      );
      if (invalidChars.length > 0) {
        validationErrors.push(
          `perfil_egreso contiene caracteres no válidos: "${invalidChars.join(
            '", "'
          )}". Please check for special characters that are not allowed.`
        );
      }
    }

    if (texto_requisito_ingreso) {
      const invalidChars = findInvalidChars(
        texto_requisito_ingreso,
        /[^A-Za-zÀ-ÖØ-öø-ÿ0-9\s.,;:()[\]{}'"¡!¿?@#$%&*+-_=<>]/g
      );
      if (invalidChars.length > 0) {
        validationErrors.push(
          `texto_requisito_ingreso contiene caracteres no válidos: "${invalidChars.join(
            '", "'
          )}". Please check for special characters that are not allowed.`
        );
      }
    }

    if (otros_requisitos) {
      const invalidChars = findInvalidChars(
        otros_requisitos,
        /[^A-Za-zÀ-ÖØ-öø-ÿ0-9\s.,;:()[\]{}'"¡!¿?@#$%&*+-_=<>-]/g
      );
      if (invalidChars.length > 0) {
        validationErrors.push(
          `otros_requisitos contiene caracteres no válidos: "${invalidChars.join(
            '", "'
          )}". Please check for special characters that are not allowed.`
        );
      }
    }

    // If validation errors exist, return them
    if (validationErrors.length > 0) {
      return res.status(400).json({
        message: "Validation errors in oferta academica data",
        errors: validationErrors,
      });
    }

    // Update oferta academica
    await ofertaAcademicaToUpdate.update({
      etapa_id: etapa_id || ofertaAcademicaToUpdate.etapa_id,
      cod_sede: cod_sede || ofertaAcademicaToUpdate.cod_sede,
      nombre_sede: nombre_sede || ofertaAcademicaToUpdate.nombre_sede,
      cod_carrera: cod_carrera || ofertaAcademicaToUpdate.cod_carrera,
      nombre_carrera: nombre_carrera || ofertaAcademicaToUpdate.nombre_carrera,
      modalidad: modalidad || ofertaAcademicaToUpdate.modalidad,
      cod_jornada: cod_jornada || ofertaAcademicaToUpdate.cod_jornada,
      version: version || ofertaAcademicaToUpdate.version,
      cod_tipo_plan_carrera:
        cod_tipo_plan_carrera || ofertaAcademicaToUpdate.cod_tipo_plan_carrera,
      caracteristicas_tipo_plan:
        caracteristicas_tipo_plan ||
        ofertaAcademicaToUpdate.caracteristicas_tipo_plan,
      duracion_estudios:
        duracion_estudios || ofertaAcademicaToUpdate.duracion_estudios,
      duracion_titulacion:
        duracion_titulacion || ofertaAcademicaToUpdate.duracion_titulacion,
      duracion_total: duracion_total || ofertaAcademicaToUpdate.duracion_total,
      regimen: regimen || ofertaAcademicaToUpdate.regimen,
      duracion_regimen:
        duracion_regimen || ofertaAcademicaToUpdate.duracion_regimen,
      nombre_titulo: nombre_titulo || ofertaAcademicaToUpdate.nombre_titulo,
      nombre_grado: nombre_grado || ofertaAcademicaToUpdate.nombre_grado,
      cod_nivel_global:
        cod_nivel_global || ofertaAcademicaToUpdate.cod_nivel_global,
      cod_nivel_carrera:
        cod_nivel_carrera || ofertaAcademicaToUpdate.cod_nivel_carrera,
      cod_demre: cod_demre || ofertaAcademicaToUpdate.cod_demre,
      anio_inicio: anio_inicio || ofertaAcademicaToUpdate.anio_inicio,
      acreditacion:
        acreditacion !== undefined
          ? acreditacion
          : ofertaAcademicaToUpdate.acreditacion,
      elegible_beca_pedagogia:
        elegible_beca_pedagogia !== undefined
          ? elegible_beca_pedagogia
          : ofertaAcademicaToUpdate.elegible_beca_pedagogia,
      ped_med_odont_otro:
        ped_med_odont_otro || ofertaAcademicaToUpdate.ped_med_odont_otro,
      requisito_ingreso:
        requisito_ingreso !== undefined
          ? requisito_ingreso
          : ofertaAcademicaToUpdate.requisito_ingreso,
      semestres_reconocidos:
        semestres_reconocidos !== undefined
          ? semestres_reconocidos
          : ofertaAcademicaToUpdate.semestres_reconocidos,
      area_actual: area_actual || ofertaAcademicaToUpdate.area_actual,
      area_destino_agricultura:
        area_destino_agricultura !== undefined
          ? area_destino_agricultura
          : ofertaAcademicaToUpdate.area_destino_agricultura,
      area_destino_ciencias:
        area_destino_ciencias !== undefined
          ? area_destino_ciencias
          : ofertaAcademicaToUpdate.area_destino_ciencias,
      area_destino_cs_sociales:
        area_destino_cs_sociales !== undefined
          ? area_destino_cs_sociales
          : ofertaAcademicaToUpdate.area_destino_cs_sociales,
      area_destino_educacion:
        area_destino_educacion !== undefined
          ? area_destino_educacion
          : ofertaAcademicaToUpdate.area_destino_educacion,
      area_destino_humanidades:
        area_destino_humanidades !== undefined
          ? area_destino_humanidades
          : ofertaAcademicaToUpdate.area_destino_humanidades,
      area_destino_ingenieria:
        area_destino_ingenieria !== undefined
          ? area_destino_ingenieria
          : ofertaAcademicaToUpdate.area_destino_ingenieria,
      area_destino_salud:
        area_destino_salud !== undefined
          ? area_destino_salud
          : ofertaAcademicaToUpdate.area_destino_salud,
      area_destino_servicios:
        area_destino_servicios !== undefined
          ? area_destino_servicios
          : ofertaAcademicaToUpdate.area_destino_servicios,
      ponderacion_nem:
        ponderacion_nem !== undefined
          ? ponderacion_nem
          : ofertaAcademicaToUpdate.ponderacion_nem,
      ponderacion_ranking:
        ponderacion_ranking !== undefined
          ? ponderacion_ranking
          : ofertaAcademicaToUpdate.ponderacion_ranking,
      ponderacion_c_lectora:
        ponderacion_c_lectora !== undefined
          ? ponderacion_c_lectora
          : ofertaAcademicaToUpdate.ponderacion_c_lectora,
      ponderacion_matematicas:
        ponderacion_matematicas !== undefined
          ? ponderacion_matematicas
          : ofertaAcademicaToUpdate.ponderacion_matematicas,
      ponderacion_matematicas_2:
        ponderacion_matematicas_2 !== undefined
          ? ponderacion_matematicas_2
          : ofertaAcademicaToUpdate.ponderacion_matematicas_2,
      ponderacion_historia:
        ponderacion_historia !== undefined
          ? ponderacion_historia
          : ofertaAcademicaToUpdate.ponderacion_historia,
      ponderacion_ciencias:
        ponderacion_ciencias !== undefined
          ? ponderacion_ciencias
          : ofertaAcademicaToUpdate.ponderacion_ciencias,
      ponderacion_otros:
        ponderacion_otros !== undefined
          ? ponderacion_otros
          : ofertaAcademicaToUpdate.ponderacion_otros,
      vacantes_primer_semestre:
        vacantes_primer_semestre !== undefined
          ? vacantes_primer_semestre
          : ofertaAcademicaToUpdate.vacantes_primer_semestre,
      vacantes_segundo_semestre:
        vacantes_segundo_semestre !== undefined
          ? vacantes_segundo_semestre
          : ofertaAcademicaToUpdate.vacantes_segundo_semestre,
      vacantes_pace:
        vacantes_pace !== undefined
          ? vacantes_pace
          : ofertaAcademicaToUpdate.vacantes_pace,
      malla_curricular:
        malla_curricular || ofertaAcademicaToUpdate.malla_curricular,
      perfil_egreso: perfil_egreso || ofertaAcademicaToUpdate.perfil_egreso,
      texto_requisito_ingreso:
        texto_requisito_ingreso ||
        ofertaAcademicaToUpdate.texto_requisito_ingreso,
      otros_requisitos:
        otros_requisitos || ofertaAcademicaToUpdate.otros_requisitos,
      mail_difusion_carrera:
        mail_difusion_carrera || ofertaAcademicaToUpdate.mail_difusion_carrera,
      vigencia_carrera:
        vigencia_carrera !== undefined
          ? vigencia_carrera
          : ofertaAcademicaToUpdate.vigencia_carrera,
      // Financial fields for Etapa 3
      formato_valor:
        formato_valor !== undefined
          ? formato_valor
          : ofertaAcademicaToUpdate.formato_valor,
      valor_matricula_anual:
        valor_matricula_anual !== undefined
          ? valor_matricula_anual
          : ofertaAcademicaToUpdate.valor_matricula_anual,
      costo_titulacion:
        costo_titulacion !== undefined
          ? costo_titulacion
          : ofertaAcademicaToUpdate.costo_titulacion,
      valor_certificado_diploma:
        valor_certificado_diploma !== undefined
          ? valor_certificado_diploma
          : ofertaAcademicaToUpdate.valor_certificado_diploma,
      arancel_anual:
        arancel_anual !== undefined
          ? arancel_anual
          : ofertaAcademicaToUpdate.arancel_anual,
    });

    res.status(200).json({
      message: "Oferta Academica updated successfully",
      data: ofertaAcademicaToUpdate,
    });
  } catch (error) {
    console.error("Error updating Oferta Academica:", error);
    res.status(500).json({
      message: "Error updating Oferta Academica",
      error: error.message,
    });
  }
};

/**
 * Delete an OAOfertaAcademica
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const deleteOAOfertaAcademica = async (req, res) => {
  try {
    const { id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Find oferta academica by ID
    const ofertaAcademica = await OAOfertaAcademica.findByPk(id);

    if (!ofertaAcademica) {
      return res.status(404).json({ message: "Oferta Academica not found" });
    }

    // Delete oferta academica
    await ofertaAcademica.destroy();

    res.status(200).json({
      message: "Oferta Academica deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting Oferta Academica:", error);
    res.status(500).json({
      message: "Error deleting Oferta Academica",
      error: error.message,
    });
  }
};

/**
 * Delete all OAOfertaAcademicas by Etapa ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const deleteOAOfertaAcademicasByEtapaId = async (req, res) => {
  try {
    const { etapa_id } = req.params;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Check if etapa exists
    const etapa = await OAEtapa.findByPk(etapa_id);
    if (!etapa) {
      return res.status(404).json({ message: "Etapa not found" });
    }

    // Use a transaction to ensure all operations succeed or fail together
    const result = await sequelize.transaction(async (t) => {
      // Find all ofertas academicas associated with this etapa
      const count = await OAOfertaAcademica.destroy({
        where: { etapa_id },
        transaction: t,
      });

      return { count };
    });

    res.status(200).json({
      message: `${result.count} Ofertas Academicas deleted successfully`,
      count: result.count,
    });
  } catch (error) {
    console.error("Error deleting Ofertas Academicas:", error);
    res.status(500).json({
      message: "Error deleting Ofertas Academicas",
      error: error.message,
    });
  }
};

/**
 * Get count of careers per etapa
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getCantCarrerasPerEtapa = async (req, res) => {
  try {
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Get count of careers per etapa
    const carrerasPerEtapa = await OAEtapa.findAll({
      attributes: [
        "id",
        "tipo",
        "etapa",
        [
          Sequelize.fn("COUNT", Sequelize.col("OAOfertaAcademicas.oa_id")),
          "carrerasCount",
        ],
      ],
      include: [
        {
          model: OAOfertaAcademica,
          attributes: [],
        },
        {
          model: OASubProceso,
          attributes: ["id", "proceso_id", "tipo"],
          include: [
            {
              model: OAProceso,
              attributes: ["id", "anio"],
            },
          ],
        },
      ],
      group: ["OAEtapa.id", "OASubProceso.id", "OASubProceso.OAProceso.id"],
      order: [["id", "ASC"]],
    });

    res.status(200).json({
      message: "Carreras per etapa retrieved successfully",
      data: carrerasPerEtapa,
    });
  } catch (error) {
    console.error("Error retrieving carreras per etapa:", error);
    res.status(500).json({
      message: "Error retrieving carreras per etapa",
      error: error.message,
    });
  }
};

/**
 * Import ofertas academicas from a CSV file for a specific etapa
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const importOfertasAcademicasFromCSV = async (req, res) => {
  try {
    const { etapa_id } = req.params;
    const { csvData } = req.body;
    const userToken = req.headers.authorization;

    // Function to determine categoria based on cod_nivel_global and cod_nivel_carrera
    const determineCategoria = (codNivelGlobal, codNivelCarrera) => {
      const nivelGlobal = parseInt(codNivelGlobal);
      const nivelCarrera = parseInt(codNivelCarrera);

      if (nivelGlobal === 1) {
        return "pregrado";
      } else if (nivelGlobal === 2) {
        // Check if it's really postgrado or postitulo based on COD_NIVEL_CARRERA
        if (nivelCarrera >= 7 && nivelCarrera <= 9) {
          return "postgrado";
        } else if (nivelCarrera === 5 || nivelCarrera === 6) {
          return "postitulo";
        } else {
          return "postgrado"; // default for nivel global 2
        }
      } else if (nivelGlobal === 3) {
        // Level 3 can be either postgrado or postitulo based on COD_NIVEL_CARRERA
        if (nivelCarrera >= 7 && nivelCarrera <= 9) {
          return "postgrado";
        } else {
          return "postitulo";
        }
      }

      return "unknown";
    };

    // Check for required parameters

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Check for missing required parameters
    if (!etapa_id) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: etapa_id",
      });
    }

    if (!csvData) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: csvData",
      });
    }

    // Check if etapa exists
    const etapa = await OAEtapa.findByPk(etapa_id);
    if (!etapa) {
      return res.status(404).json({
        message: "Etapa not found",
      });
    }
    const subproceso = await OASubProceso.findOne({
      where: { id: etapa.subproceso_id },
    });
    if (!subproceso) {
      return res.status(404).json({
        message: `Subproceso not found for etapa ${etapa.id}`,
      });
    }
    // Assume csvData is already parsed on the frontend
    let dataToProcess = csvData;
    // console.log(dataToProcess);

    // Normalize column names (convert to lowercase) and map CSV column names to model field names
    const normalizedOfertas = dataToProcess.map((oferta) => {
      const normalizedOferta = {};
      for (const key in oferta) {
        // Convert key to lowercase
        const lowerKey = key.toLowerCase();
        normalizedOferta[lowerKey] = oferta[key];
      }
      return normalizedOferta;
    });

    console.log("normalizedOfertas");
    console.log(normalizedOfertas);

    normalizedOfertas.forEach((oferta) => {
      if (oferta.nombre_sede)
        oferta.nombre_sede = oferta.nombre_sede.toUpperCase();
      if (oferta.nombre_carrera)
        oferta.nombre_carrera = oferta.nombre_carrera.toUpperCase();
      if (oferta.caracteristicas_tipo_plan)
        oferta.caracteristicas_tipo_plan =
          oferta.caracteristicas_tipo_plan.toUpperCase();
      if (oferta.nombre_titulo)
        oferta.nombre_titulo = oferta.nombre_titulo.toUpperCase();
      if (oferta.nombre_grado)
        oferta.nombre_grado = oferta.nombre_grado.toUpperCase();
    });

    // Validate categoria consistency
    const categoriaErrors = [];
    normalizedOfertas.forEach((oferta, index) => {
      if (oferta.cod_nivel_global && oferta.cod_nivel_carrera) {
        const determinedCategoria = determineCategoria(oferta.cod_nivel_global, oferta.cod_nivel_carrera);

        if (determinedCategoria !== "unknown" && etapa.categoria && determinedCategoria !== etapa.categoria) {
          categoriaErrors.push({
            row: index + 1,
            field: "categoria",
            type: "categoria_mismatch",
            expected: etapa.categoria,
            found: determinedCategoria,
            message: `La categoría determinada '${determinedCategoria}' no coincide con la categoría de la etapa '${etapa.categoria}' en la fila ${index + 1}`
          });
        }
      }
    });

    // If there are categoria errors, return them immediately
    if (categoriaErrors.length > 0) {
      const errorsByRow = {};

      categoriaErrors.forEach((error) => {
        const row = error.row;
        if (!errorsByRow[row]) {
          errorsByRow[row] = [];
        }
        errorsByRow[row].push(error.message);
      });

      const csvLines = [];
      Object.keys(errorsByRow).forEach((row) => {
        const messages = errorsByRow[row].join("; ");
        csvLines.push(`Fila ${row}: ${messages}`);
      });

      const errores = csvLines.join("\n");
      return res.status(400).json({
        message: "Categoria validation errors in ofertas academicas data",
        errors: errores,
      });
    }

    // Validate ofertas data
    const validationErrors = validateOfertasAcademicasData(
      normalizedOfertas,
      etapa,
      subproceso
    );

    const errorsByRow = {};

    validationErrors.forEach((error) => {
      const row = error.row;
      if (!errorsByRow[row]) {
        errorsByRow[row] = [];
      }
      errorsByRow[row].push(error.message);
    });

    // Convert to CSV format
    const csvLines = [];

    Object.keys(errorsByRow).forEach((row) => {
      const messages = errorsByRow[row].join("; ");
      csvLines.push(`Fila ${row}: ${messages}`);
    });

    const errores = csvLines.join("\n");
    if (errores.length > 0) {
      return res.status(400).json({
        message: "Validation errors in ofertas academicas data",
        errors: errores,
      });
    }

    // No need to pre-process data as it should be properly formatted from the frontend

    // Create transaction to ensure all operations succeed or fail together
    const result = await sequelize.transaction(async (t) => {
      const createdOfertas = [];

      // Create ofertas academicas
      for (const oferta of normalizedOfertas) {
        // Convert empty strings to null for proper validation, but preserve 0 values for numeric fields
        const cleanedOferta = {};
        const numericFieldsWithZero = [
          'elegible_beca_pedagogia',
          'duracion_titulacion',
          'semestres_reconocidos',
          'vacantes_segundo_semestre',
          'vacantes_primer_semestre',
          'vacantes_pace',
          'cod_demre',
          'ponderacion_nem',
          'ponderacion_ranking',
          'ponderacion_c_lectora',
          'ponderacion_matematicas',
          'ponderacion_matematicas_2',
          'ponderacion_historia',
          'ponderacion_ciencias',
          'ponderacion_otros',
          'costo_titulacion',
          'valor_matricula_anual',
          'valor_certificado_diploma',
          'arancel_anual'
        ];

        for (const [key, value] of Object.entries(oferta)) {
          if (value === undefined) {
            cleanedOferta[key] = null;
          } else if (value === "" || (typeof value === 'string' && value.trim() === "")) {
            cleanedOferta[key] = null;
          } else if (numericFieldsWithZero.includes(key) && (value === 0 || value === "0")) {
            // Preserve 0 values for numeric fields as numbers
            cleanedOferta[key] = 0;
          } else {
            cleanedOferta[key] = value;
          }
        }

        const newOferta = await OAOfertaAcademica.create(
          {
            etapa_id,
            cod_sede: cleanedOferta.cod_sede,
            nombre_sede: cleanedOferta.nombre_sede,
            cod_carrera: cleanedOferta.cod_carrera,
            nombre_carrera: cleanedOferta.nombre_carrera,
            modalidad: cleanedOferta.modalidad,
            cod_jornada: cleanedOferta.cod_jornada,
            version: cleanedOferta.version,
            cod_tipo_plan_carrera: cleanedOferta.cod_tipo_plan_carrera,
            caracteristicas_tipo_plan: cleanedOferta.caracteristicas_tipo_plan,
            duracion_estudios: cleanedOferta.duracion_estudios,
            duracion_titulacion: cleanedOferta.duracion_titulacion,
            duracion_total: cleanedOferta.duracion_total,
            regimen: cleanedOferta.regimen,
            duracion_regimen: cleanedOferta.duracion_regimen,
            nombre_titulo: cleanedOferta.nombre_titulo,
            nombre_grado: cleanedOferta.nombre_grado,
            cod_nivel_global: cleanedOferta.cod_nivel_global,
            cod_nivel_carrera: cleanedOferta.cod_nivel_carrera,
            cod_demre: cleanedOferta.cod_demre,
            anio_inicio: cleanedOferta.anio_inicio,
            acreditacion: cleanedOferta.acreditacion,
            elegible_beca_pedagogia: cleanedOferta.elegible_beca_pedagogia,
            ped_med_odont_otro: cleanedOferta.ped_med_odont_otro,
            requisito_ingreso: cleanedOferta.requisito_ingreso,
            semestres_reconocidos: cleanedOferta.semestres_reconocidos,
            area_actual: cleanedOferta.area_actual,
            area_destino_agricultura: cleanedOferta.area_destino_agricultura,
            area_destino_ciencias: cleanedOferta.area_destino_ciencias,
            area_destino_cs_sociales: cleanedOferta.area_destino_cs_sociales,
            area_destino_educacion: cleanedOferta.area_destino_educacion,
            area_destino_humanidades: cleanedOferta.area_destino_humanidades,
            area_destino_ingenieria: cleanedOferta.area_destino_ingenieria,
            area_destino_salud: cleanedOferta.area_destino_salud,
            area_destino_servicios: cleanedOferta.area_destino_servicios,
            ponderacion_nem: cleanedOferta.ponderacion_nem,
            ponderacion_ranking: cleanedOferta.ponderacion_ranking,
            ponderacion_c_lectora: cleanedOferta.ponderacion_c_lectora,
            ponderacion_matematicas: cleanedOferta.ponderacion_matematicas,
            ponderacion_matematicas_2: cleanedOferta.ponderacion_matematicas_2,
            ponderacion_historia: cleanedOferta.ponderacion_historia,
            ponderacion_ciencias: cleanedOferta.ponderacion_ciencias,
            ponderacion_otros: cleanedOferta.ponderacion_otros,
            vacantes_primer_semestre: cleanedOferta.vacantes_primer_semestre,
            vacantes_segundo_semestre: cleanedOferta.vacantes_segundo_semestre,
            vacantes_pace: cleanedOferta.vacantes_pace,
            malla_curricular: cleanedOferta.malla_curricular,
            perfil_egreso: cleanedOferta.perfil_egreso,
            texto_requisito_ingreso: cleanedOferta.texto_requisito_ingreso,
            otros_requisitos: cleanedOferta.otros_requisitos,
            mail_difusion_carrera: cleanedOferta.mail_difusion_carrera,
            vigencia_carrera: cleanedOferta.vigencia_carrera,
            // Financial fields for Etapa 3
            formato_valor: cleanedOferta.formato_valor,
            valor_matricula_anual: cleanedOferta.valor_matricula_anual,
            costo_titulacion: cleanedOferta.costo_titulacion,
            valor_certificado_diploma: cleanedOferta.valor_certificado_diploma,
            arancel_anual: cleanedOferta.arancel_anual,
          },
          { transaction: t }
        );

        createdOfertas.push(newOferta);
      }

      return {
        ofertasCount: createdOfertas.length,
      };
    });
    const subido_por = await getEmailFromToken(userToken);

    // Update etapa with subido_por
    await etapa.update({
      subido_por,
      fecha_carga: new Date(),
    });

    res.status(201).json({
      message: "Ofertas academicas imported successfully",
      ofertasCount: result.ofertasCount,
    });
  } catch (error) {
    console.error("Error importing ofertas academicas:", error);

    // Check if error is from validation
    if (error.message.startsWith("[") && error.message.endsWith("]")) {
      try {
        const validationErrors = JSON.parse(error.message);
        return res.status(400).json({
          message: "Validation errors in ofertas academicas data",
          errors: validationErrors,
        });
      } catch (e) {
        // If parsing fails, return the original error
      }
    }

    res.status(500).json({
      message: "Error importing ofertas academicas",
      error: error.message,
    });
  }
};
/**
 * Delete all OAOfertaAcademicas for a given subproceso, etapa and tipo, for all 3 categories
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const deleteOAOfertaAcademicasBySubprocesoEtapaTipo = async (
  req,
  res
) => {
  try {
    const { subproceso_id, etapa_number, tipo } = req.body;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Check for required parameters
    if (!subproceso_id) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: subproceso_id",
      });
    }
    if (!etapa_number) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: etapa_number",
      });
    }
    if (!tipo) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: tipo",
      });
    }

    // Find all etapas for this subproceso, etapa and tipo (all categories)
    const etapas = await OAEtapa.findAll({
      where: {
        subproceso_id,
        etapa: etapa_number,
        tipo,
      },
    });

    if (!etapas || etapas.length === 0) {
      return res.status(404).json({
        message: "No se encontraron etapas para los parámetros dados",
      });
    }

    // Delete all OAOfertaAcademica for each etapa
    let totalDeleted = 0;
    for (const etapa of etapas) {
      const deleted = await OAOfertaAcademica.destroy({
        where: { etapa_id: etapa.id },
      });
      totalDeleted += deleted;
    }

    res.status(200).json({
      message: `Se eliminaron ${totalDeleted} ofertas académicas para el subproceso, etapa y tipo indicados (todas las categorías)`,
      totalDeleted,
    });
  } catch (error) {
    console.error(
      "Error deleting ofertas academicas by subproceso/etapa/tipo:",
      error
    );
    res.status(500).json({
      message: "Error deleting ofertas academicas by subproceso/etapa/tipo",
      error: error.message,
    });
  }
};
export const importOfertasAcademicasFromCSVTotal = async (req, res) => {
  try {
    const { csvData, subproceso_id, etapa_number, tipo, categoria } = req.body;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Check for missing required parameters
    if (!subproceso_id) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: subproceso_id",
      });
    }

    if (!csvData || !Array.isArray(csvData)) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: csvData (debe ser un array)",
      });
    }

    if (!etapa_number) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: etapa_number",
      });
    }

    if (!tipo) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: tipo",
      });
    }

    if (!categoria) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: categoria",
      });
    }

    // Check if subproceso exists
    const subproceso = await OASubProceso.findByPk(subproceso_id);
    if (!subproceso) {
      return res.status(404).json({
        message: "Subproceso not found",
      });
    }

    // Find all etapas that match the criteria
    const etapas = await OAEtapa.findAll({
      where: {
        subproceso_id,
        etapa: etapa_number,
        tipo,
      },
    });

    if (etapas.length === 0) {
      return res.status(404).json({
        message: `No se encontraron etapas para subproceso_id: ${subproceso_id}, etapa: ${etapa_number}, tipo: ${tipo}`,
      });
    }
    for (const etapa of etapas) {
      await OAOfertaAcademica.destroy({
        where: { etapa_id: etapa.id },
      });
    }
    // Debug: Log the first few rows of raw CSV data
    console.log("Raw CSV data (first 2 rows):");
    console.log(csvData.slice(0, 2));

    // Normalize column names (convert to lowercase) and clean empty values
    const normalizedOfertas = csvData.map((oferta, index) => {
      const normalizedOferta = {};
      for (const key in oferta) {
        const lowerKey = key.toLowerCase();
        const value = oferta[key];

        // Debug: Log column mapping for first row
        if (index === 0) {
          console.log(`Column mapping: "${key}" -> "${lowerKey}" = "${value}"`);
        }

        // Define numeric fields where 0 is a valid value that should be preserved
        const numericFieldsWithZero = [
          'elegible_beca_pedagogia',
          'duracion_titulacion',
          'semestres_reconocidos',
          'vacantes_segundo_semestre',
          'vacantes_primer_semestre',
          'vacantes_pace',
          'cod_demre',
          'ponderacion_nem',
          'ponderacion_ranking',
          'ponderacion_c_lectora',
          'ponderacion_matematicas',
          'ponderacion_matematicas_2',
          'ponderacion_historia',
          'ponderacion_ciencias',
          'ponderacion_otros',
          'costo_titulacion',
          'valor_matricula_anual',
          'valor_certificado_diploma',
          'arancel_anual'
        ];

        // Convert empty strings to null for proper validation, but preserve 0 values for numeric fields
        if (value === undefined) {
          normalizedOferta[lowerKey] = null;
        } else if (value === "" || (typeof value === 'string' && value.trim() === "")) {
          normalizedOferta[lowerKey] = null;
        } else if (numericFieldsWithZero.includes(lowerKey) && (value === 0 || value === "0")) {
          // Preserve 0 values for numeric fields as numbers
          normalizedOferta[lowerKey] = 0;
        } else {
          normalizedOferta[lowerKey] = value;
        }
      }
      return normalizedOferta;
    });

    // Apply text transformations
    normalizedOfertas.forEach((oferta) => {
      if (oferta.nombre_sede && typeof oferta.nombre_sede === 'string')
        oferta.nombre_sede = oferta.nombre_sede.toUpperCase();
      if (oferta.nombre_carrera && typeof oferta.nombre_carrera === 'string')
        oferta.nombre_carrera = oferta.nombre_carrera.toUpperCase();
      if (oferta.caracteristicas_tipo_plan && typeof oferta.caracteristicas_tipo_plan === 'string')
        oferta.caracteristicas_tipo_plan =
          oferta.caracteristicas_tipo_plan.toUpperCase();
      if (oferta.nombre_titulo && typeof oferta.nombre_titulo === 'string')
        oferta.nombre_titulo = oferta.nombre_titulo.toUpperCase();
      if (oferta.nombre_grado && typeof oferta.nombre_grado === 'string')
        oferta.nombre_grado = oferta.nombre_grado.toUpperCase();
    });

    // Function to determine categoria based on COD_NIVEL_GLOBAL and COD_NIVEL_CARRERA
    const determineCategoria = (codNivelGlobal, codNivelCarrera) => {
      const nivelGlobal = parseInt(codNivelGlobal);
      const nivelCarrera = parseInt(codNivelCarrera);

      if (nivelGlobal === 1) {
        return "pregrado";
      } else if (nivelGlobal === 2) {
        // Check if it's really postgrado or postitulo based on COD_NIVEL_CARRERA
        if (nivelCarrera >= 7 && nivelCarrera <= 9) {
          return "postgrado";
        } else if (nivelCarrera === 5 || nivelCarrera === 6) {
          return "postitulo";
        } else {
          return "postgrado"; // default for nivel global 2
        }
      } else if (nivelGlobal === 3) {
        // Level 3 can be either postgrado or postitulo based on COD_NIVEL_CARRERA
        if (nivelCarrera >= 7 && nivelCarrera <= 9) {
          return "postgrado";
        } else {
          return "postitulo";
        }
      }

      return "unknown";
    };

    // Separate ofertas by categoria
    const ofertasPorCategoria = {
      pregrado: [],
      postgrado: [],
      postitulo: [],
    };

    normalizedOfertas.forEach((oferta, index) => {
      const ofertaCategoria = determineCategoria(
        oferta.cod_nivel_global,
        oferta.cod_nivel_carrera
      );

      if (ofertasPorCategoria[ofertaCategoria]) {
        ofertasPorCategoria[ofertaCategoria].push({
          ...oferta,
          originalIndex: index,
        });
      } else {
        console.warn(
          `Oferta en fila ${
            index + 1
          } tiene categoría desconocida: ${ofertaCategoria}`
        );
      }
    });

    // Create a map of etapas by categoria
    const etapasPorCategoria = {};
    etapas.forEach((etapa) => {
      etapasPorCategoria[etapa.categoria] = etapa;
    });

    // Validate that we have etapas for all categorias that have data
    const categoriasConDatos = Object.keys(ofertasPorCategoria).filter(
      (cat) => ofertasPorCategoria[cat].length > 0
    );
    const categoriasSinEtapa = categoriasConDatos.filter(
      (cat) => !etapasPorCategoria[cat]
    );

    if (categoriasSinEtapa.length > 0) {
      return res.status(404).json({
        message: `No se encontraron etapas para las siguientes categorías: ${categoriasSinEtapa.join(
          ", "
        )}`,
      });
    }

    // Category-specific validation (add your logic here)
    const categoriaValidationErrors = [];
    // Example: if you want to add specific rules per category
    // if (categoria === "pregrado") {
    //   // Add pregrado-specific validation
    // } else if (categoria === "postgrado") {
    //   // Add postgrado-specific validation
    // } else if (categoria === "postitulo") {
    //   // Add postitulo-specific validation
    // }
    if (categoriaValidationErrors.length > 0) {
      return res.status(400).json({
        message: "Category-specific validation errors",
        errors: categoriaValidationErrors,
      });
    }

    const allValidationErrors = [];

    for (const [categoria, ofertas] of Object.entries(ofertasPorCategoria)) {
      if (ofertas.length === 0) continue;

      const etapa = etapasPorCategoria[categoria];
      const validationErrors = validateOfertasAcademicasData(
        ofertas,
        etapa,
        subproceso
      );

      // Add categoria and etapa info to errors
      validationErrors.forEach((error) => {
        error.categoria = categoria;
        error.etapa_id = etapa.id;
      });

      allValidationErrors.push(...validationErrors);
    }

    // Process validation errors
    if (allValidationErrors.length > 0) {
      const errorsByRow = {};

      allValidationErrors.forEach((error) => {
        const row = error.row;
        if (!errorsByRow[row]) {
          errorsByRow[row] = [];
        }
        errorsByRow[row].push(`${error.categoria}: ${error.message}`);
      });

      const csvLines = [];
      Object.keys(errorsByRow).forEach((row) => {
        const messages = errorsByRow[row].join("; ");
        csvLines.push(`Fila ${row}: ${messages}`);
      });

      const errores = csvLines.join("\n");

      return res.status(400).json({
        message: "Validation errors in ofertas academicas data",
        errors: errores,
      });
    }

    // Create transaction to ensure all operations succeed or fail together
    const result = await sequelize.transaction(async (t) => {
      const resultadoPorCategoria = {};
      let totalCreated = 0;

      // Process each categoria
      for (const [categoria, ofertas] of Object.entries(ofertasPorCategoria)) {
        if (ofertas.length === 0) continue;

        const etapa = etapasPorCategoria[categoria];
        const createdOfertas = [];

        // Create ofertas academicas for this categoria
        for (const oferta of ofertas) {
          const newOferta = await OAOfertaAcademica.create(
            {
              etapa_id: etapa.id,
              cod_sede: oferta.cod_sede,
              nombre_sede: oferta.nombre_sede,
              cod_carrera: oferta.cod_carrera,
              nombre_carrera: oferta.nombre_carrera,
              modalidad: oferta.modalidad,
              cod_jornada: oferta.cod_jornada,
              version: oferta.version,
              cod_tipo_plan_carrera: oferta.cod_tipo_plan_carrera,
              caracteristicas_tipo_plan: oferta.caracteristicas_tipo_plan,
              duracion_estudios: oferta.duracion_estudios,
              duracion_titulacion: oferta.duracion_titulacion,
              duracion_total: oferta.duracion_total,
              regimen: oferta.regimen,
              duracion_regimen: oferta.duracion_regimen,
              nombre_titulo: oferta.nombre_titulo,
              nombre_grado: oferta.nombre_grado,
              cod_nivel_global: oferta.cod_nivel_global,
              cod_nivel_carrera: oferta.cod_nivel_carrera,
              cod_demre: oferta.cod_demre,
              anio_inicio: oferta.anio_inicio,
              acreditacion: oferta.acreditacion,
              elegible_beca_pedagogia: oferta.elegible_beca_pedagogia,
              ped_med_odont_otro: oferta.ped_med_odont_otro,
              requisito_ingreso: oferta.requisito_ingreso,
              semestres_reconocidos: oferta.semestres_reconocidos,
              area_actual: oferta.area_actual,
              area_destino_agricultura: oferta.area_destino_agricultura,
              area_destino_ciencias: oferta.area_destino_ciencias,
              area_destino_cs_sociales: oferta.area_destino_cs_sociales,
              area_destino_educacion: oferta.area_destino_educacion,
              area_destino_humanidades: oferta.area_destino_humanidades,
              area_destino_ingenieria: oferta.area_destino_ingenieria,
              area_destino_salud: oferta.area_destino_salud,
              area_destino_servicios: oferta.area_destino_servicios,
              ponderacion_nem: oferta.ponderacion_nem,
              ponderacion_ranking: oferta.ponderacion_ranking,
              ponderacion_c_lectora: oferta.ponderacion_c_lectora,
              ponderacion_matematicas: oferta.ponderacion_matematicas,
              ponderacion_matematicas_2: oferta.ponderacion_matematicas_2,
              ponderacion_historia: oferta.ponderacion_historia,
              ponderacion_ciencias: oferta.ponderacion_ciencias,
              ponderacion_otros: oferta.ponderacion_otros,
              vacantes_primer_semestre: oferta.vacantes_primer_semestre,
              vacantes_segundo_semestre: oferta.vacantes_segundo_semestre,
              vacantes_pace: oferta.vacantes_pace,
              malla_curricular: oferta.malla_curricular,
              perfil_egreso: oferta.perfil_egreso,
              texto_requisito_ingreso: oferta.texto_requisito_ingreso,
              otros_requisitos: oferta.otros_requisitos,
              mail_difusion_carrera: oferta.mail_difusion_carrera,
              vigencia_carrera: oferta.vigencia_carrera,
              // Financial fields for Etapa 3
              formato_valor: oferta.formato_valor,
              valor_matricula_anual: oferta.valor_matricula_anual,
              costo_titulacion: oferta.costo_titulacion,
              valor_certificado_diploma: oferta.valor_certificado_diploma,
              arancel_anual: oferta.arancel_anual,
            },
            { transaction: t }
          );

          createdOfertas.push(newOferta);
        }

        resultadoPorCategoria[categoria] = {
          etapa_id: etapa.id,
          count: createdOfertas.length,
        };

        totalCreated += createdOfertas.length;
      }

      return {
        resultadoPorCategoria,
        totalCount: totalCreated,
      };
    });

    // Update all etapas with subido_por and fecha_carga
    const subido_por = await getEmailFromToken(userToken);

    const updatePromises = Object.values(etapasPorCategoria).map((etapa) =>
      etapa.update({
        subido_por,
        fecha_carga: new Date(),
      })
    );

    await Promise.all(updatePromises);

    res.status(201).json({
      message: "Ofertas academicas imported successfully to multiple etapas",
      totalCount: result.totalCount,
      resultadoPorCategoria: result.resultadoPorCategoria,
    });
  } catch (error) {
    console.error("Error importing ofertas academicas totals:", error);

    // Check if error is from validation
    if (error.message.startsWith("[") && error.message.endsWith("]")) {
      try {
        const validationErrors = JSON.parse(error.message);
        return res.status(400).json({
          message: "Validation errors in ofertas academicas data",
          errors: validationErrors,
        });
      } catch (e) {
        // If parsing fails, return the original error
      }
    }

    res.status(500).json({
      message: "Error importing ofertas academicas totals",
      error: error.message,
    });
  }
};
/**
 * Validate ofertas academicas data
 * @param {Array} ofertas - Array of ofertas academicas objects
 * @returns {Array} - Array of validation errors
 */
const validateOfertasAcademicasData = (ofertas, etapa, subproceso) => {
  const errors = [];
  console.log("ofertas");

  console.log("etapa.etapa");
  console.log(etapa.etapa);
  if (subproceso.tipo === 2) {
    // Validation for Subproceso 2 (Oferta Académica 2025)
    return validateSubproceso2(ofertas, etapa, subproceso);
  }
  if (subproceso.tipo === 1) {
    // Etapa 1
    // Basic validation for Etapa 1

    if (etapa.tipo === "insumo") {
      return errors; // No validation needed for insumo type
    } else {
      const requiredFields = [
        "cod_sede",
        "nombre_sede",
        //"cod_carrera",
        "nombre_carrera",
        "modalidad",
        "cod_jornada",
        "version",
        "cod_tipo_plan_carrera",
        "caracteristicas_tipo_plan",
        "duracion_estudios",
        "duracion_titulacion",
        "duracion_total",
        "regimen",
        "duracion_regimen",
        "nombre_titulo",
        "nombre_grado",
        "cod_nivel_global",
        "cod_nivel_carrera",
        "cod_demre",
        "anio_inicio",
        "acreditacion",
        "elegible_beca_pedagogia",
        "ped_med_odont_otro",
        "requisito_ingreso",
        "semestres_reconocidos",
        "area_actual",
        "ponderacion_nem",
        "ponderacion_ranking",
        "ponderacion_c_lectora",
        "ponderacion_matematicas",
        "ponderacion_matematicas_2",
        "ponderacion_historia",
        "ponderacion_ciencias",
        "ponderacion_otros",
        "vacantes_primer_semestre",
        "vacantes_segundo_semestre",
        "vacantes_pace",
        "vigencia_carrera",
        // The following fields can be empty:
        // "malla_curricular",
        // "perfil_egreso",
        // "texto_requisito_ingreso",
        // "otros_requisitos",
        // "mail_difusion_carrera"
      ];
      const fieldsToValidate = [
        "nombre_sede",
        "nombre_carrera",
        "caracteristicas_tipo_plan",
        "nombre_titulo",
        "nombre_grado",
      ];
      const numericFields = [
        "cod_sede",
        "cod_carrera",
        "cod_jornada",
        "version",
        "cod_tipo_plan_carrera",
        "duracion_estudios",
        "duracion_titulacion",
        "duracion_total",
        "cod_nivel_global",
        "cod_nivel_carrera",
        "cod_demre",
        "anio_inicio",
        "acreditacion",
        "elegible_beca_pedagogia",
        "requisito_ingreso",
        "semestres_reconocidos",
        "area_actual",
        "area_destino_agricultura",
        "area_destino_ciencias",
        "area_destino_cs_sociales",
        "area_destino_educacion",
        "area_destino_humanidades",
        "area_destino_ingenieria",
        "area_destino_salud",
        "area_destino_servicios",
        "ponderacion_nem",
        "ponderacion_ranking",
        "ponderacion_c_lectora",
        "ponderacion_matematicas",
        "ponderacion_matematicas_2",
        "ponderacion_historia",
        "ponderacion_ciencias",
        "ponderacion_otros",
        "vacantes_primer_semestre",
        "vacantes_segundo_semestre",
        "vacantes_pace",
        "vigencia_carrera",
      ];
      ofertas.forEach((oferta, index) => {
        if (
          oferta.cod_carrera === undefined ||
          oferta.cod_carrera === null ||
          oferta.cod_carrera === ""
        ) {
          if (etapa.etapa === 1) {
            errors.push({
              row: index + 1,
              field: "cod_carrera",
              type: "required_field",
              message: "cod_carrera is required for etapa 1",
            });
          }
        } else {
          if (etapa.etapa === 2) {
            errors.push({
              row: index + 1,
              field: "cod_carrera",
              type: "should_be_empty",
              message: "cod_carrera debe estar vacío para etapa 2",
            });
          }
        }

        const missingFields = [];

        // Define numeric fields where 0 is a valid value
        const numericFieldsWithZero = [
          'elegible_beca_pedagogia',
          'duracion_titulacion',
          'semestres_reconocidos',
          'vacantes_segundo_semestre',
          'vacantes_primer_semestre',
          'vacantes_pace',
          'cod_demre',
          'ponderacion_nem',
          'ponderacion_ranking',
          'ponderacion_c_lectora',
          'ponderacion_matematicas',
          'ponderacion_matematicas_2',
          'ponderacion_historia',
          'ponderacion_ciencias',
          'ponderacion_otros'
        ];

        requiredFields.forEach((field) => {
          let isEmpty = false;

          if (numericFieldsWithZero.includes(field)) {
            // For numeric fields, only consider null, undefined, or empty string as missing
            isEmpty = oferta[field] === null || oferta[field] === undefined || oferta[field] === "";
          } else {
            // For non-numeric fields, use the original logic
            isEmpty = oferta[field] === undefined || oferta[field] === null || oferta[field] === "";
          }

          if (isEmpty) {
            missingFields.push(field);
          }
        });

        if (missingFields.length > 0) {
          errors.push({
            row: index + 1,
            fields: missingFields,
            type: "campos_requeridos",
            message: `Faltan los siguientes campos requeridos: ${missingFields.join(
              ", "
            )}`,
          });
        }
        // Validate vigencia_carrera
        if (
          oferta.vigencia_carrera !== undefined &&
          oferta.vigencia_carrera !== null &&
          oferta.vigencia_carrera !== "" &&
          ![1, 2, 3].includes(parseInt(oferta.vigencia_carrera))
        ) {
          errors.push({
            row: index + 1,
            field: "vigencia_carrera",
            type: "valor_invalido",
            value: oferta.vigencia_carrera,
            message: `vigencia_carrera debe ser 1, 2, o 3`,
          });
        } else {
          if (etapa.etapa === 1) {
            // Check for required fields if vigencia is 1 (active)
            if (
              oferta.vigencia_carrera &&
              parseInt(oferta.vigencia_carrera) === 1
            ) {
              // Malla curricular required for active programs
              if (
                !oferta.malla_curricular ||
                oferta.malla_curricular.trim() === ""
              ) {
                errors.push({
                  row: index + 1,
                  field: "malla_curricular",
                  type: "required_field",
                  message: `malla_curricular is required when vigencia_carrera is 1 (active)`,
                });
              }

              // Perfil egreso required for active programs
              if (!oferta.perfil_egreso || oferta.perfil_egreso.trim() === "") {
                errors.push({
                  row: index + 1,
                  field: "perfil_egreso",
                  type: "required_field",
                  message: `perfil_egreso is required when vigencia_carrera is 1 (active)`,
                });
              }

              // Mail difusion required for active programs
              if (
                !oferta.mail_difusion_carrera ||
                oferta.mail_difusion_carrera.trim() === ""
              ) {
                errors.push({
                  row: index + 1,
                  field: "mail_difusion_carrera",
                  type: "required_field",
                  message: `mail_difusion_carrera is required when vigencia_carrera is 1 (active)`,
                });
              }
            }
            //VIGENCIA_CARRERA
            //MALLA_CURRICULAR
            //PERFIL_EGRESO
            //MAIL_DIFUSION_CARRERA
          } else {
            if (
              !oferta.malla_curricular ||
              oferta.malla_curricular.trim() === ""
            ) {
              errors.push({
                row: index + 1,
                field: "malla_curricular",
                type: "required_field",
                message: `malla_curricular is required in etapa 2`,
              });
            }

            // Perfil egreso required for active programs
            if (!oferta.perfil_egreso || oferta.perfil_egreso.trim() === "") {
              errors.push({
                row: index + 1,
                field: "perfil_egreso",
                type: "required_field",
                message: `perfil_egreso is required in etapa 2`,
              });
            }

            // Mail difusion required for active programs
            if (
              !oferta.mail_difusion_carrera ||
              oferta.mail_difusion_carrera.trim() === ""
            ) {
              errors.push({
                row: index + 1,
                field: "mail_difusion_carrera",
                type: "required_field",
                message: `mail_difusion_carrera is required in etapa 2`,
              });
            }
          }
        }

        fieldsToValidate.forEach((field) => {
          validateMayusNumeros(oferta, field, index, errors);
        });

        // Validate numeric fields
        numericFields.forEach((field) => {
          if (isNaN(Number(oferta[field]))) {
            errors.push({
              row: index + 1,
              field: field,
              type: "invalid_number",
              value: oferta[field],
              message: `${field} debe ser un numero valido`,
            });
          }
        });
        // Validate modalidad
        if (![1, 2, 3].includes(parseInt(oferta.modalidad))) {
          errors.push({
            row: index + 1,
            field: "modalidad",
            type: "valor_invalido",
            value: oferta.modalidad,
            message: `modalidad debe ser 1 (Presencial), 2 (Semipresencial), o 3 (No Presencial)`,
          });
        }
        // Validate cod_jornada
        if (![1, 2, 3, 4, 5].includes(parseInt(oferta.cod_jornada))) {
          errors.push({
            row: index + 1,
            field: "cod_jornada",
            type: "valor_invalido",
            value: oferta.cod_jornada,
            message: `cod_jornada debe ser 1 (Diurna), 2 (Vespertina), 3 (Semi Presencial), 4 (A Distancia), o 5 (Otra)`,
          });
        }
        // Validate cod_tipo_plan_carrera
        if (
          oferta.cod_tipo_plan_carrera !== undefined &&
          oferta.cod_tipo_plan_carrera !== null &&
          oferta.cod_tipo_plan_carrera !== "" &&
          ![1, 2, 3].includes(parseInt(oferta.cod_tipo_plan_carrera))
        ) {
          errors.push({
            row: index + 1,
            field: "cod_tipo_plan_carrera",
            type: "valor_invalido",
            value: oferta.cod_tipo_plan_carrera,
            message: `cod_tipo_plan_carrera debe ser 1 (Plan Regular), 2 (Plan Especial), o 3 (Plan Regular de Continuidad)`,
          });
        }
        // Validate regimen
        if (
          oferta.regimen !== undefined &&
          oferta.regimen !== null &&
          oferta.regimen !== "" &&
          ![1, 2, 3, 4].includes(parseInt(oferta.regimen))
        ) {
          errors.push({
            row: index + 1,
            field: "regimen",
            type: "valor_invalido",
            value: oferta.regimen,
            message: `regimen debe ser 1 (Semestres), 2 (Trimestres), 3 (Años), o 4 (Bimestres)`,
          });
        }
        // Validate cod_nivel_global - Subproceso 1 only accepts Pregrado (1)
        if (
          oferta.cod_nivel_global !== undefined &&
          oferta.cod_nivel_global !== null &&
          oferta.cod_nivel_global !== ""
        ) {
          const codNivelGlobal = parseInt(oferta.cod_nivel_global);
          if (codNivelGlobal !== 1) {
            errors.push({
              row: index + 1,
              field: "cod_nivel_global",
              type: "valor_invalido",
              value: oferta.cod_nivel_global,
              message: `cod_nivel_global debe ser 1 (Pregrado) para Subproceso 1 (Oferta-Acceso). Este proceso solo considera programas de pregrado.`,
            });
          }
        }
        // Validate cod_nivel_carrera
        if (
          oferta.cod_nivel_carrera !== undefined &&
          oferta.cod_nivel_carrera !== null &&
          oferta.cod_nivel_carrera !== "" &&
          ![0, 1, 2, 3, 4].includes(parseInt(oferta.cod_nivel_carrera))
        ) {
          errors.push({
            row: index + 1,
            field: "cod_nivel_carrera",
            type: "valor_invalido",
            value: oferta.cod_nivel_carrera,
            message: `cod_nivel_carrera debe ser 0 (Bachillerato, Ciclo inicial o Plan Común), 1 (Técnica de Nivel Superior), 2 (Profesional sin Licenciatura), 3 (Licenciatura no conducente a título), o 4 (Profesional con Licenciatura)`,
          });
        }
        // Validate acreditacion (1: Sí, 2: No)
        if (
          oferta.acreditacion !== undefined &&
          oferta.acreditacion !== null &&
          oferta.acreditacion !== "" &&
          ![1, 2].includes(parseInt(oferta.acreditacion))
        ) {
          errors.push({
            row: index + 1,
            field: "acreditacion",
            type: "valor_invalido",
            value: oferta.acreditacion,
            message: `acreditacion debe ser 1 (Sí) o 2 (No)`,
          });
        }

        // Validate elegible_beca_pedagogia (0, 1, 2, 3)
        if (
          oferta.elegible_beca_pedagogia !== undefined &&
          oferta.elegible_beca_pedagogia !== null &&
          oferta.elegible_beca_pedagogia !== "" &&
          ![0, 1, 2, 3].includes(parseInt(oferta.elegible_beca_pedagogia))
        ) {
          errors.push({
            row: index + 1,
            field: "elegible_beca_pedagogia",
            type: "valor_invalido",
            value: oferta.elegible_beca_pedagogia,
            message: `elegible_beca_pedagogia debe ser 0 (No elegible), 1 (Programa de Pedagogía Elegible), 2 (Programa de Licenciatura Elegible), o 3 (Programa de Formación Pedagógica Elegible)`,
          });
        }
        // Validate ped_med_odont_otro
        if (
          oferta.ped_med_odont_otro !== undefined &&
          oferta.ped_med_odont_otro !== null &&
          oferta.ped_med_odont_otro !== "" &&
          !["P", "M", "D", "O"].includes(oferta.ped_med_odont_otro)
        ) {
          errors.push({
            row: index + 1,
            field: "ped_med_odont_otro",
            type: "valor_invalido",
            value: oferta.ped_med_odont_otro,
            message: `ped_med_odont_otro debe ser 'P' (Pedagogía), 'M' (Medicina), 'D' (Odontología), o 'O' (Otro)`,
          });
        }
        // Validate requisito_ingreso (1-10)
        if (
          oferta.requisito_ingreso !== undefined &&
          oferta.requisito_ingreso !== null &&
          oferta.requisito_ingreso !== "" &&
          ![1, 2, 3, 4, 5, 6, 7, 8, 9, 10].includes(
            parseInt(oferta.requisito_ingreso)
          )
        ) {
          errors.push({
            row: index + 1,
            field: "requisito_ingreso",
            type: "valor_invalido",
            value: oferta.requisito_ingreso,
            message: `requisito_ingreso debe ser un número between 1 and 10 (1: Educación Media, 2: Técnico de Nivel Superior, 3: Bachillerato, 4: Ciclo Básico, 5: Plan Común, 6: Título Profesional, 7: Licenciatura, 8: Especialidad Médica u Odontológica, 9: Postítulo, 10: Magíster)`,
          });
        }

        // Validate area_actual
        if (
          oferta.area_actual !== undefined &&
          oferta.area_actual !== null &&
          oferta.area_actual !== "" &&
          ![1, 2, 3, 4, 5, 6, 7, 8].includes(parseInt(oferta.area_actual))
        ) {
          errors.push({
            row: index + 1,
            field: "area_actual",
            type: "valor_invalido",
            value: oferta.area_actual,
            message: `area_actual debe ser 1 (Agricultura), 
      2 (Ciencias), 3 (Ciencias Sociales, Enseñanza Comercial y Derecho),
       4 (Educación), 5 (Humanidades y Artes), 6 (Ingeniería, Industria y Construcción), 7 (Salud), o 8 (Servicios)`,
          });
        }

        // Validate area_destino_agricultura (0 o 1)
        if (
          oferta.area_destino_agricultura !== undefined &&
          oferta.area_destino_agricultura !== null &&
          oferta.area_destino_agricultura !== "" &&
          ![0, 1].includes(parseInt(oferta.area_destino_agricultura))
        ) {
          errors.push({
            row: index + 1,
            field: "area_destino_agricultura",
            type: "valor_invalido",
            value: oferta.area_destino_agricultura,
            message: `area_destino_agricultura debe ser 0 (no beneficio de continuidad) o 1 (sí existe beneficio de continuidad)`,
          });
        }

        // Validate area_destino_ciencias (0 o 1)
        if (
          oferta.area_destino_ciencias !== undefined &&
          oferta.area_destino_ciencias !== null &&
          oferta.area_destino_ciencias !== "" &&
          ![0, 1].includes(parseInt(oferta.area_destino_ciencias))
        ) {
          errors.push({
            row: index + 1,
            field: "area_destino_ciencias",
            type: "valor_invalido",
            value: oferta.area_destino_ciencias,
            message: `area_destino_ciencias debe ser 0 (no beneficio de continuidad) o 1 (sí existe beneficio de continuidad)`,
          });
        }

        // Validate area_destino_cs_sociales (0 o 1)
        if (
          oferta.area_destino_cs_sociales !== undefined &&
          oferta.area_destino_cs_sociales !== null &&
          oferta.area_destino_cs_sociales !== "" &&
          ![0, 1].includes(parseInt(oferta.area_destino_cs_sociales))
        ) {
          errors.push({
            row: index + 1,
            field: "area_destino_cs_sociales",
            type: "valor_invalido",
            value: oferta.area_destino_cs_sociales,
            message: `area_destino_cs_sociales debe ser 0 (no beneficio de continuidad) o 1 (sí existe beneficio de continuidad)`,
          });
        }

        // Validate area_destino_educacion (0 o 1)
        if (
          oferta.area_destino_educacion !== undefined &&
          oferta.area_destino_educacion !== null &&
          oferta.area_destino_educacion !== "" &&
          ![0, 1].includes(parseInt(oferta.area_destino_educacion))
        ) {
          errors.push({
            row: index + 1,
            field: "area_destino_educacion",
            type: "valor_invalido",
            value: oferta.area_destino_educacion,
            message: `area_destino_educacion debe ser 0 (no beneficio de continuidad) o 1 (sí existe beneficio de continuidad)`,
          });
        }

        // Validate area_destino_humanidades (0 o 1)
        if (
          oferta.area_destino_humanidades !== undefined &&
          oferta.area_destino_humanidades !== null &&
          oferta.area_destino_humanidades !== "" &&
          ![0, 1].includes(parseInt(oferta.area_destino_humanidades))
        ) {
          errors.push({
            row: index + 1,
            field: "area_destino_humanidades",
            type: "valor_invalido",
            value: oferta.area_destino_humanidades,
            message: `area_destino_humanidades debe ser 0 (no beneficio de continuidad) o 1 (sí existe beneficio de continuidad)`,
          });
        }

        // Validate area_destino_ingenieria (0 o 1)
        if (
          oferta.area_destino_ingenieria !== undefined &&
          oferta.area_destino_ingenieria !== null &&
          oferta.area_destino_ingenieria !== "" &&
          ![0, 1].includes(parseInt(oferta.area_destino_ingenieria))
        ) {
          errors.push({
            row: index + 1,
            field: "area_destino_ingenieria",
            type: "valor_invalido",
            value: oferta.area_destino_ingenieria,
            message: `area_destino_ingenieria debe ser 0 (no beneficio de continuidad) o 1 (sí existe beneficio de continuidad)`,
          });
        }

        // Validate area_destino_salud (0 o 1)
        if (
          oferta.area_destino_salud !== undefined &&
          oferta.area_destino_salud !== null &&
          oferta.area_destino_salud !== "" &&
          ![0, 1].includes(parseInt(oferta.area_destino_salud))
        ) {
          errors.push({
            row: index + 1,
            field: "area_destino_salud",
            type: "valor_invalido",
            value: oferta.area_destino_salud,
            message: `area_destino_salud debe ser 0 (no beneficio de continuidad) o 1 (sí existe beneficio de continuidad)`,
          });
        }

        // Validate area_destino_servicios (0 o 1)
        if (
          oferta.area_destino_servicios !== undefined &&
          oferta.area_destino_servicios !== null &&
          oferta.area_destino_servicios !== "" &&
          ![0, 1].includes(parseInt(oferta.area_destino_servicios))
        ) {
          errors.push({
            row: index + 1,
            field: "area_destino_servicios",
            type: "valor_invalido",
            value: oferta.area_destino_servicios,
            message: `area_destino_servicios debe ser 0 (no beneficio de continuidad) o 1 (sí existe beneficio de continuidad)`,
          });
        }

        if (
          oferta.ponderacion_nem !== undefined &&
          (oferta.ponderacion_nem < 0 || oferta.ponderacion_nem > 100)
        ) {
          errors.push({
            row: index + 1,
            field: "ponderacion_nem",
            type: "range_error",
            value: oferta.ponderacion_nem,
            message: `ponderacion_nem debe estar entre 0 y 100`,
          });
        }

        if (
          oferta.ponderacion_ranking !== undefined &&
          (oferta.ponderacion_ranking < 0 || oferta.ponderacion_ranking > 100)
        ) {
          errors.push({
            row: index + 1,
            field: "ponderacion_ranking",
            type: "range_error",
            value: oferta.ponderacion_ranking,
            message: `ponderacion_ranking debe estar entre 0 y 100`,
          });
        }

        if (
          oferta.ponderacion_c_lectora !== undefined &&
          (oferta.ponderacion_c_lectora < 0 ||
            oferta.ponderacion_c_lectora > 100)
        ) {
          errors.push({
            row: index + 1,
            field: "ponderacion_c_lectora",
            type: "range_error",
            value: oferta.ponderacion_c_lectora,
            message: `ponderacion_c_lectora debe estar entre 0 y 100`,
          });
        }

        if (
          oferta.ponderacion_matematicas !== undefined &&
          (oferta.ponderacion_matematicas < 0 ||
            oferta.ponderacion_matematicas > 100)
        ) {
          errors.push({
            row: index + 1,
            field: "ponderacion_matematicas",
            type: "range_error",
            value: oferta.ponderacion_matematicas,
            message: `ponderacion_matematicas debe estar entre 0 y 100`,
          });
        }

        if (
          oferta.ponderacion_matematicas_2 !== undefined &&
          oferta.ponderacion_matematicas_2 !== null &&
          oferta.ponderacion_matematicas_2 !== "" &&
          (oferta.ponderacion_matematicas_2 < 0 ||
            oferta.ponderacion_matematicas_2 > 100)
        ) {
          errors.push({
            row: index + 1,
            field: "ponderacion_matematicas_2",
            type: "range_error",
            value: oferta.ponderacion_matematicas_2,
            message: `ponderacion_matematicas_2 debe estar entre 0 y 100`,
          });
        }

        if (
          oferta.ponderacion_historia !== undefined &&
          oferta.ponderacion_historia !== null &&
          oferta.ponderacion_historia !== "" &&
          (oferta.ponderacion_historia < 0 || oferta.ponderacion_historia > 100)
        ) {
          errors.push({
            row: index + 1,
            field: "ponderacion_historia",
            type: "range_error",
            value: oferta.ponderacion_historia,
            message: `ponderacion_historia debe estar entre 0 y 100`,
          });
        }

        if (
          oferta.ponderacion_ciencias !== undefined &&
          (oferta.ponderacion_ciencias < 0 || oferta.ponderacion_ciencias > 100)
        ) {
          errors.push({
            row: index + 1,
            field: "ponderacion_ciencias",
            type: "range_error",
            value: oferta.ponderacion_ciencias,
            message: `ponderacion_ciencias debe estar entre 0 y 100`,
          });
        }

        if (
          oferta.ponderacion_otros !== undefined &&
          oferta.ponderacion_otros !== null &&
          oferta.ponderacion_otros !== "" &&
          (oferta.ponderacion_otros < 0 || oferta.ponderacion_otros > 100)
        ) {
          errors.push({
            row: index + 1,
            field: "ponderacion_otros",
            type: "range_error",
            value: oferta.ponderacion_otros,
            message: `ponderacion_otros debe estar entre 0 y 100`,
          });
        }

        // Validate acreditacion
        if (
          oferta.acreditacion !== undefined &&
          ![1, 2].includes(parseInt(oferta.acreditacion))
        ) {
          errors.push({
            row: index + 1,
            field: "acreditacion",
            type: "valor_invalido",
            value: oferta.acreditacion,
            message: `acreditacion debe ser 1 o 2`,
          });
        }

        // Validate elegible_beca_pedagogia
        if (
          oferta.elegible_beca_pedagogia !== undefined &&
          oferta.elegible_beca_pedagogia !== null &&
          oferta.elegible_beca_pedagogia !== "" &&
          ![0, 1, 2, 3].includes(parseInt(oferta.elegible_beca_pedagogia))
        ) {
          errors.push({
            row: index + 1,
            field: "elegible_beca_pedagogia",
            type: "valor_invalido",
            value: oferta.elegible_beca_pedagogia,
            message: `elegible_beca_pedagogia debe ser 0, 1, 2, o 3`,
          });
        }

        // Validate ped_med_odont_otro
        if (
          oferta.ped_med_odont_otro !== undefined &&
          !["P", "M", "D", "O"].includes(oferta.ped_med_odont_otro)
        ) {
          errors.push({
            row: index + 1,
            field: "ped_med_odont_otro",
            type: "valor_invalido",
            value: oferta.ped_med_odont_otro,
            message: `ped_med_odont_otro debe ser 'P', 'M', 'D' o 'O' `,
          });
        }

        // Validate requisito_ingreso
        if (
          oferta.requisito_ingreso !== undefined &&
          oferta.requisito_ingreso !== null &&
          oferta.requisito_ingreso !== "" &&
          ![1, 2, 3, 4, 5, 6, 7, 8, 9, 10].includes(
            parseInt(oferta.requisito_ingreso)
          )
        ) {
          errors.push({
            row: index + 1,
            field: "requisito_ingreso",
            type: "valor_invalido",
            value: oferta.requisito_ingreso,
            message: `requisito_ingreso debe ser un número entre 1 y 10`,
          });
        }

        // Validate semestres_reconocidos - this field is optional
        if (
          oferta.semestres_reconocidos !== undefined &&
          oferta.semestres_reconocidos !== null &&
          oferta.semestres_reconocidos !== "" &&
          isNaN(parseInt(oferta.semestres_reconocidos))
        ) {
          errors.push({
            row: index + 1,
            field: "semestres_reconocidos",
            type: "valor_invalido",
            value: oferta.semestres_reconocidos,
            message: `semestres_reconocidos debe ser un número`,
          });
        }

        // Validate vacantes_segundo_semestre - this field is optional
        if (
          oferta.vacantes_segundo_semestre !== undefined &&
          oferta.vacantes_segundo_semestre !== null &&
          oferta.vacantes_segundo_semestre !== "" &&
          isNaN(parseInt(oferta.vacantes_segundo_semestre))
        ) {
          errors.push({
            row: index + 1,
            field: "vacantes_segundo_semestre",
            type: "valor_invalido",
            value: oferta.vacantes_segundo_semestre,
            message: `vacantes_segundo_semestre debe ser un número`,
          });
        }

        // Validate cod_carrera if not empty
        if (
          oferta.vacantes_segundo_semestre !== undefined &&
          oferta.vacantes_segundo_semestre !== null &&
          oferta.vacantes_segundo_semestre !== "" &&
          isNaN(parseInt(oferta.vacantes_segundo_semestre))
        ) {
          errors.push({
            row: index + 1,
            field: "cod_carrera",
            type: "invalid_format",
            value: oferta.cod_carrera,
            message: `cod_carrera debe contener solo números`,
          });
        }
        // Validate email if not empty
        if (
          oferta.mail_difusion_carrera &&
          oferta.mail_difusion_carrera.trim() !== "" &&
          !isValidEmail(oferta.mail_difusion_carrera)
        ) {
          errors.push({
            row: index + 1,
            field: "mail_difusion_carrera",
            type: "invalid_format",
            value: oferta.mail_difusion_carrera,
            message: `mail_difusion_carrera debe ser un correo electrónico válido`,
          });
        }

        // Validate URL if not empty
        if (
          oferta.malla_curricular &&
          oferta.malla_curricular.trim() !== "" &&
          !isValidURL(oferta.malla_curricular)
        ) {
          errors.push({
            row: index + 1,
            field: "malla_curricular",
            type: "invalid_format",
            value: oferta.malla_curricular,
            message: `malla_curricular debe ser una URL válida`,
          });
        }
      });

      return errors;
    }
  }
};

function validateMayusNumeros(oferta, fieldName, index, errors) {
  const value = oferta[fieldName];
  if (value) {
    const invalidChars = findInvalidChars(value, /[áéíóúüña-z]/g);
    if (invalidChars.length > 0) {
      errors.push({
        row: index + 1,
        field: fieldName,
        invalidChars: invalidChars,
        message: `${fieldName} contiene caracteres no válidos: "${invalidChars.join(
          '", "'
        )}". Solo se permiten letras mayúsculas (A-Z), números y espacios.`,
      });
    }
  }
}

/**
 * Validate email
 * @param {string} email - Email to validate
 * @returns {boolean} - True if email is valid, false otherwise
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate ofertas academicas data for Subproceso 2 (Oferta Académica 2025)
 * @param {Array} ofertas - Array of ofertas academicas objects
 * @param {Object} etapa - Etapa object
 * @param {Object} subproceso - Subproceso object
 * @returns {Array} - Array of validation errors
 */
const validateSubproceso2 = (ofertas, etapa, subproceso) => {
  const errors = [];

  console.log("Validating Subproceso 2 - Etapa:", etapa.etapa);

  // Define fields that need uppercase validation
  const fieldsToValidate = [
    "nombre_sede",
    "nombre_carrera",
    "caracteristicas_tipo_plan",
    "nombre_titulo",
    "nombre_grado"
  ];

  // Define numeric fields for validation
  const numericFields = [
    "cod_sede",
    "modalidad",
    "cod_jornada",
    "version",
    "cod_tipo_plan_carrera",
    "duracion_estudios",
    "duracion_titulacion",
    "duracion_total",
    "regimen",
    "duracion_regimen",
    "cod_nivel_global",
    "cod_nivel_carrera",
    "anio_inicio",
    "acreditacion",
    "elegible_beca_pedagogia",
    "requisito_ingreso",
    "semestres_reconocidos",
    "area_actual"
  ];

  ofertas.forEach((oferta, index) => {
    // Basic field validation
    fieldsToValidate.forEach((field) => {
      validateMayusNumeros(oferta, field, index, errors);
    });

    // Validate numeric fields
    numericFields.forEach((field) => {
      if (oferta[field] !== null && oferta[field] !== undefined && oferta[field] !== "" && isNaN(Number(oferta[field]))) {
        errors.push({
          row: index + 1,
          field: field,
          type: "invalid_number",
          value: oferta[field],
          message: `${field} debe ser un numero valido`,
        });
      }
    });

    // Etapa specific validations for Subproceso 2
    if (etapa.etapa === 1) {
      validateSubproceso2Etapa1(oferta, index, errors, etapa);
    } else if (etapa.etapa === 2) {
      validateSubproceso2Etapa2(oferta, index, errors, etapa);
    } else if (etapa.etapa === 3) {
      validateSubproceso2Etapa3(oferta, index, errors, etapa);
    }
  });

  return errors;
};

/**
 * Validate specific rules for Subproceso 2, Etapa 1 (Oferta Vigente)
 * @param {Object} oferta - Single oferta object
 * @param {number} index - Index of the oferta in the array
 * @param {Array} errors - Array to push errors to
 * @param {Object} etapa - Etapa object
 */
const validateSubproceso2Etapa1 = (oferta, index, errors, etapa) => {
  // Condición 1: Modalidad y Jornada combinations for Oferta Vigente
  const validModalidadJornadaCombinations = [
    { modalidad: 1, jornada: 1 }, // Presencial - Diurna
    { modalidad: 1, jornada: 2 }, // Presencial - Vespertina
    { modalidad: 1, jornada: 5 }, // Presencial - Otra
    { modalidad: 2, jornada: 1 }, // Semipresencial - Diurna
    { modalidad: 2, jornada: 2 }, // Semipresencial - Vespertina
    { modalidad: 2, jornada: 3 }, // Semipresencial - Semipresencial
    { modalidad: 2, jornada: 5 }, // Semipresencial - Otra
    { modalidad: 3, jornada: 4 }  // No Presencial - A Distancia
  ];

  if (oferta.modalidad && oferta.cod_jornada) {
    const modalidad = parseInt(oferta.modalidad);
    const jornada = parseInt(oferta.cod_jornada);

    const isValidCombination = validModalidadJornadaCombinations.some(
      combo => combo.modalidad === modalidad && combo.jornada === jornada
    );

    if (!isValidCombination) {
      errors.push({
        row: index + 1,
        field: "modalidad_jornada",
        type: "invalid_combination",
        value: `modalidad: ${modalidad}, jornada: ${jornada}`,
        message: `La combinación de modalidad ${modalidad} y jornada ${jornada} no es válida para Oferta Vigente`,
      });
    }
  }

  // Condición 3: Duration validations
  if (oferta.duracion_estudios && oferta.duracion_titulacion && oferta.duracion_total) {
    const de = parseInt(oferta.duracion_estudios);
    const dt = parseInt(oferta.duracion_titulacion);
    const df = parseInt(oferta.duracion_total);

    // DE <= DF
    if (de > df) {
      errors.push({
        row: index + 1,
        field: "duracion_total",
        type: "duration_validation",
        message: `La duración total (${df}) no puede ser menor que la duración de estudios (${de})`,
      });
    }

    // DE + DT >= DF
    if ((de + dt) < df) {
      errors.push({
        row: index + 1,
        field: "duracion_total",
        type: "duration_validation",
        message: `La suma de duración de estudios (${de}) y duración de titulación (${dt}) debe ser mayor o igual a la duración total (${df})`,
      });
    }
  }

  // Condición 5: Duration limit validation
  if (oferta.duracion_estudios && parseInt(oferta.duracion_estudios) > 14) {
    errors.push({
      row: index + 1,
      field: "duracion_estudios",
      type: "duration_limit",
      value: oferta.duracion_estudios,
      message: `La duración de estudios no puede exceder 14 semestres`,
    });
  }

  // Validate modalidad values
  if (oferta.modalidad && ![1, 2, 3].includes(parseInt(oferta.modalidad))) {
    errors.push({
      row: index + 1,
      field: "modalidad",
      type: "valor_invalido",
      value: oferta.modalidad,
      message: `modalidad debe ser 1 (Presencial), 2 (Semipresencial), o 3 (No Presencial)`,
    });
  }

  // Validate jornada values
  if (oferta.cod_jornada && ![1, 2, 3, 4, 5].includes(parseInt(oferta.cod_jornada))) {
    errors.push({
      row: index + 1,
      field: "cod_jornada",
      type: "valor_invalido",
      value: oferta.cod_jornada,
      message: `cod_jornada debe ser 1 (Diurno), 2 (Vespertino), 3 (Semipresencial), 4 (A Distancia), o 5 (Otro)`,
    });
  }

  // Validate vigencia_carrera
  if (oferta.vigencia_carrera && ![1, 2, 3].includes(parseInt(oferta.vigencia_carrera))) {
    errors.push({
      row: index + 1,
      field: "vigencia_carrera",
      type: "valor_invalido",
      value: oferta.vigencia_carrera,
      message: `vigencia_carrera debe ser 1 (Vigente con estudiantes nuevos), 2 (Vigente sin estudiantes nuevos), o 3 (No vigente)`,
    });
  }

  // Validate regimen values
  if (oferta.regimen && ![1, 2, 3, 4].includes(parseInt(oferta.regimen))) {
    errors.push({
      row: index + 1,
      field: "regimen",
      type: "valor_invalido",
      value: oferta.regimen,
      message: `regimen debe ser 1 (Semestres), 2 (Trimestres), 3 (Años), o 4 (Bimestres)`,
    });
  }

  // Validate when regimen is 1 (Semestres), duracion_regimen should equal duracion_total
  if (oferta.regimen && parseInt(oferta.regimen) === 1 && oferta.duracion_regimen && oferta.duracion_total) {
    if (parseInt(oferta.duracion_regimen) !== parseInt(oferta.duracion_total)) {
      errors.push({
        row: index + 1,
        field: "duracion_regimen",
        type: "regimen_validation",
        message: `Cuando el régimen es 1 (Semestres), la duración del régimen debe ser igual a la duración total`,
      });
    }
  }

  // Validate vacantes when vigencia is 1
  if (oferta.vigencia_carrera && parseInt(oferta.vigencia_carrera) === 1) {
    const vacantes1 = parseInt(oferta.vacantes_primer_semestre) || 0;
    const vacantes2 = parseInt(oferta.vacantes_segundo_semestre) || 0;

    if (vacantes1 + vacantes2 === 0) {
      errors.push({
        row: index + 1,
        field: "vacantes",
        type: "required_field",
        message: `Cuando la vigencia es 1, las vacantes semestrales no pueden ser 0`,
      });
    }
  }

  // Validate vacantes when vigencia is 2 or 3
  if (oferta.vigencia_carrera && [2, 3].includes(parseInt(oferta.vigencia_carrera))) {
    if (oferta.vacantes_primer_semestre && parseInt(oferta.vacantes_primer_semestre) !== 0) {
      errors.push({
        row: index + 1,
        field: "vacantes_primer_semestre",
        type: "invalid_value",
        message: `Cuando la vigencia es 2 o 3, las vacantes del primer semestre deben ser 0`,
      });
    }

    if (oferta.vacantes_segundo_semestre && parseInt(oferta.vacantes_segundo_semestre) !== 0) {
      errors.push({
        row: index + 1,
        field: "vacantes_segundo_semestre",
        type: "invalid_value",
        message: `Cuando la vigencia es 2 o 3, las vacantes del segundo semestre deben ser 0`,
      });
    }
  }

  // Validate acreditacion values
  if (oferta.acreditacion && ![1, 2].includes(parseInt(oferta.acreditacion))) {
    errors.push({
      row: index + 1,
      field: "acreditacion",
      type: "valor_invalido",
      value: oferta.acreditacion,
      message: `acreditacion debe ser 1 (Acreditada) o 2 (No acreditada)`,
    });
  }

  // Validate elegible_beca_pedagogia values
  if (oferta.elegible_beca_pedagogia && ![0, 1, 2, 3].includes(parseInt(oferta.elegible_beca_pedagogia))) {
    errors.push({
      row: index + 1,
      field: "elegible_beca_pedagogia",
      type: "valor_invalido",
      value: oferta.elegible_beca_pedagogia,
      message: `elegible_beca_pedagogia debe ser 0 (No elegible), 1 (Pedagogía elegible), 2 (Licenciatura elegible), o 3 (Formación pedagógica elegible)`,
    });
  }

  // Validate ped_med_odont_otro values
  if (oferta.ped_med_odont_otro && !['P', 'M', 'D', 'O'].includes(oferta.ped_med_odont_otro)) {
    errors.push({
      row: index + 1,
      field: "ped_med_odont_otro",
      type: "valor_invalido",
      value: oferta.ped_med_odont_otro,
      message: `ped_med_odont_otro debe ser P (Pedagogía), M (Medicina), D (Odontología), u O (Otro)`,
    });
  }

  // Validate requisito_ingreso values (1-10)
  if (oferta.requisito_ingreso && (parseInt(oferta.requisito_ingreso) < 1 || parseInt(oferta.requisito_ingreso) > 10)) {
    errors.push({
      row: index + 1,
      field: "requisito_ingreso",
      type: "valor_invalido",
      value: oferta.requisito_ingreso,
      message: `requisito_ingreso debe estar entre 1 y 10 según lo descrito en el instructivo`,
    });
  }

  // Validate semestres_reconocidos (0-14)
  if (oferta.semestres_reconocidos && (parseInt(oferta.semestres_reconocidos) < 0 || parseInt(oferta.semestres_reconocidos) > 14)) {
    errors.push({
      row: index + 1,
      field: "semestres_reconocidos",
      type: "valor_invalido",
      value: oferta.semestres_reconocidos,
      message: `semestres_reconocidos debe estar entre 0 y 14 semestres`,
    });
  }

  // Validate area_actual values (1-8)
  if (oferta.area_actual && (parseInt(oferta.area_actual) < 1 || parseInt(oferta.area_actual) > 8)) {
    errors.push({
      row: index + 1,
      field: "area_actual",
      type: "valor_invalido",
      value: oferta.area_actual,
      message: `area_actual debe estar entre 1 y 8 según lo descrito en el instructivo`,
    });
  }

  // Validate area_destino fields (0 or 1)
  const areaDestinoFields = [
    'area_destino_agricultura',
    'area_destino_ciencias',
    'area_destino_cs_sociales',
    'area_destino_educacion',
    'area_destino_humanidades',
    'area_destino_ingenieria',
    'area_destino_salud',
    'area_destino_servicios'
  ];

  areaDestinoFields.forEach(field => {
    if (oferta[field] !== undefined && oferta[field] !== null && oferta[field] !== "" && ![0, 1].includes(parseInt(oferta[field]))) {
      errors.push({
        row: index + 1,
        field: field,
        type: "valor_invalido",
        value: oferta[field],
        message: `${field} debe ser 0 (no beneficio de continuidad) o 1 (sí existe beneficio de continuidad)`,
      });
    }
  });

  // Validate ponderaciones (0-100)
  const ponderacionFields = [
    'ponderacion_nem',
    'ponderacion_ranking',
    'ponderacion_c_lectora',
    'ponderacion_matematicas',
    'ponderacion_matematicas_2',
    'ponderacion_historia',
    'ponderacion_ciencias',
    'ponderacion_otros'
  ];

  ponderacionFields.forEach(field => {
    if (oferta[field] !== undefined && oferta[field] !== null && oferta[field] !== "") {
      const value = parseFloat(oferta[field]);
      if (value < 0 || value > 100) {
        errors.push({
          row: index + 1,
          field: field,
          type: "range_error",
          value: oferta[field],
          message: `${field} debe estar entre 0 y 100`,
        });
      }
    }
  });

  // Validate sum of ponderaciones (100-160)
  const ponderacionSum = ponderacionFields.reduce((sum, field) => {
    const value = parseFloat(oferta[field]) || 0;
    return sum + value;
  }, 0);

  if (ponderacionSum > 0 && (ponderacionSum < 100 || ponderacionSum > 160)) {
    errors.push({
      row: index + 1,
      field: "ponderaciones",
      type: "sum_validation",
      value: ponderacionSum,
      message: `La suma de las ponderaciones debe estar entre 100% y 160%`,
    });
  }

  // Validate vacantes >= 0
  const vacantesFields = ['vacantes_primer_semestre', 'vacantes_segundo_semestre', 'vacantes_pace'];
  vacantesFields.forEach(field => {
    if (oferta[field] !== undefined && oferta[field] !== null && oferta[field] !== "") {
      const value = parseInt(oferta[field]);
      if (value < 0) {
        errors.push({
          row: index + 1,
          field: field,
          type: "negative_value",
          value: oferta[field],
          message: `${field} debe ser mayor o igual a 0`,
        });
      }
    }
  });

  // Validate anio_inicio not posterior to 2025
  if (oferta.anio_inicio && parseInt(oferta.anio_inicio) > 2025) {
    errors.push({
      row: index + 1,
      field: "anio_inicio",
      type: "future_year",
      value: oferta.anio_inicio,
      message: `anio_inicio no puede ser posterior a 2025`,
    });
  }

  // Validate email format for mail_difusion_carrera
  if (oferta.mail_difusion_carrera && !isValidEmail(oferta.mail_difusion_carrera)) {
    errors.push({
      row: index + 1,
      field: "mail_difusion_carrera",
      type: "invalid_email",
      value: oferta.mail_difusion_carrera,
      message: `mail_difusion_carrera debe tener un formato de email válido`,
    });
  }

  // Condición 2: Validate nivel_global and nivel_carrera combinations
  if (oferta.cod_nivel_global && oferta.cod_nivel_carrera) {
    const nivelGlobal = parseInt(oferta.cod_nivel_global);
    const nivelCarrera = parseInt(oferta.cod_nivel_carrera);

    // Validate nivel_global values (1, 2, 3)
    if (![1, 2, 3].includes(nivelGlobal)) {
      errors.push({
        row: index + 1,
        field: "cod_nivel_global",
        type: "valor_invalido",
        value: oferta.cod_nivel_global,
        message: `cod_nivel_global debe ser 1 (Pregrado), 2 (Posgrado), o 3 (Postítulo)`,
      });
    }

    // Validate nivel_carrera values (0-9)
    if (nivelCarrera < 0 || nivelCarrera > 9) {
      errors.push({
        row: index + 1,
        field: "cod_nivel_carrera",
        type: "valor_invalido",
        value: oferta.cod_nivel_carrera,
        message: `cod_nivel_carrera debe estar entre 0 y 9 según el instructivo`,
      });
    }

    // Validate combinations based on nivel_global
    if (nivelGlobal === 1 && ![0, 1, 2, 3, 4].includes(nivelCarrera)) {
      errors.push({
        row: index + 1,
        field: "cod_nivel_carrera",
        type: "invalid_combination",
        message: `Cuando cod_nivel_global es 1 (Pregrado), cod_nivel_carrera puede ser 0, 1, 2, 3, o 4`,
      });
    }

    if (nivelGlobal === 2 && ![8, 9].includes(nivelCarrera)) {
      errors.push({
        row: index + 1,
        field: "cod_nivel_carrera",
        type: "invalid_combination",
        message: `Cuando cod_nivel_global es 2 (Posgrado), cod_nivel_carrera puede ser 8 o 9`,
      });
    }

    if (nivelGlobal === 3 && ![5, 6, 7].includes(nivelCarrera)) {
      errors.push({
        row: index + 1,
        field: "cod_nivel_carrera",
        type: "invalid_combination",
        message: `Cuando cod_nivel_global es 3 (Postítulo), cod_nivel_carrera puede ser 5, 6, o 7`,
      });
    }
  }

  // Validate cod_tipo_plan_carrera values (1, 2, 3)
  if (oferta.cod_tipo_plan_carrera && ![1, 2, 3].includes(parseInt(oferta.cod_tipo_plan_carrera))) {
    errors.push({
      row: index + 1,
      field: "cod_tipo_plan_carrera",
      type: "valor_invalido",
      value: oferta.cod_tipo_plan_carrera,
      message: `cod_tipo_plan_carrera debe ser 1 (Plan Regular), 2 (Plan Especial), o 3 (Plan Regular de Continuidad)`,
    });
  }

  // When cod_tipo_plan_carrera is 1 (Plan Regular), semestres_reconocidos must be 0
  if (oferta.cod_tipo_plan_carrera && parseInt(oferta.cod_tipo_plan_carrera) === 1) {
    if (oferta.semestres_reconocidos && parseInt(oferta.semestres_reconocidos) !== 0) {
      errors.push({
        row: index + 1,
        field: "semestres_reconocidos",
        type: "plan_regular_validation",
        message: `Cuando cod_tipo_plan_carrera es 1 (Plan Regular), semestres_reconocidos debe ser 0`,
      });
    }
  }

  // Validate duration limits
  const durationFields = ['duracion_estudios', 'duracion_titulacion', 'duracion_total'];
  durationFields.forEach(field => {
    if (oferta[field] !== undefined && oferta[field] !== null && oferta[field] !== "") {
      const value = parseInt(oferta[field]);
      if (field === 'duracion_estudios' && (value <= 0 || value > 24)) {
        errors.push({
          row: index + 1,
          field: field,
          type: "duration_limit",
          value: oferta[field],
          message: `${field} debe estar entre 1 y 24 semestres`,
        });
      } else if (field === 'duracion_titulacion' && (value < 0 || value > 24)) {
        errors.push({
          row: index + 1,
          field: field,
          type: "duration_limit",
          value: oferta[field],
          message: `${field} debe estar entre 0 y 24 semestres`,
        });
      } else if (field === 'duracion_total' && (value <= 0 || value > 24)) {
        errors.push({
          row: index + 1,
          field: field,
          type: "duration_limit",
          value: oferta[field],
          message: `${field} debe estar entre 1 y 24 semestres`,
        });
      }
    }
  });

  // Validate duracion_regimen > 0
  if (oferta.duracion_regimen && parseInt(oferta.duracion_regimen) <= 0) {
    errors.push({
      row: index + 1,
      field: "duracion_regimen",
      type: "positive_value",
      value: oferta.duracion_regimen,
      message: `duracion_regimen debe ser mayor a 0`,
    });
  }

  // Validate specific requirements based on nivel_carrera
  if (oferta.cod_nivel_carrera) {
    const nivelCarrera = parseInt(oferta.cod_nivel_carrera);

    // For nivel_carrera 0, 2, 3, 4: requisito_ingreso cannot be 8, 9, 10 (Especialidad Médica, Postítulo, Magíster)
    if ([0, 2, 3, 4].includes(nivelCarrera) && oferta.requisito_ingreso && [8, 9, 10].includes(parseInt(oferta.requisito_ingreso))) {
      errors.push({
        row: index + 1,
        field: "requisito_ingreso",
        type: "invalid_requirement",
        message: `Cuando cod_nivel_carrera es ${nivelCarrera}, requisito_ingreso no puede ser Especialidad Médica (8), Postítulo (9), o Magíster (10)`,
      });
    }

    // For nivel_carrera 1 (Técnico): cannot have requisito_ingreso 6, 7, 8, 9, 10
    if (nivelCarrera === 1 && oferta.requisito_ingreso && [6, 7, 8, 9, 10].includes(parseInt(oferta.requisito_ingreso))) {
      errors.push({
        row: index + 1,
        field: "requisito_ingreso",
        type: "invalid_requirement",
        message: `Técnico de Nivel Superior no puede tener requisito de ingreso de Título Profesional, Licenciatura, Especialidad Médica, Postítulo o Magíster`,
      });
    }
  }
};

/**
 * Validate specific rules for Subproceso 2, Etapa 2 (Oferta Nueva)
 * @param {Object} oferta - Single oferta object
 * @param {number} index - Index of the oferta in the array
 * @param {Array} errors - Array to push errors to
 * @param {Object} etapa - Etapa object
 */
const validateSubproceso2Etapa2 = (oferta, index, errors, etapa) => {
  // Etapa 2 has similar validations to Etapa 1 but for new academic offerings
  // Most fields are modifiable in this stage

  // Validate modalidad and jornada combinations (same as Etapa 1)
  const validModalidadJornadaCombinations = [
    { modalidad: 1, jornada: 1 }, // Presencial - Diurna
    { modalidad: 1, jornada: 2 }, // Presencial - Vespertina
    { modalidad: 1, jornada: 5 }, // Presencial - Otra
    { modalidad: 2, jornada: 1 }, // Semipresencial - Diurna
    { modalidad: 2, jornada: 2 }, // Semipresencial - Vespertina
    { modalidad: 2, jornada: 3 }, // Semipresencial - Semipresencial
    { modalidad: 2, jornada: 5 }, // Semipresencial - Otra
    { modalidad: 3, jornada: 4 }  // No Presencial - A Distancia
  ];

  if (oferta.modalidad && oferta.cod_jornada) {
    const modalidad = parseInt(oferta.modalidad);
    const jornada = parseInt(oferta.cod_jornada);

    const isValidCombination = validModalidadJornadaCombinations.some(
      combo => combo.modalidad === modalidad && combo.jornada === jornada
    );

    if (!isValidCombination) {
      errors.push({
        row: index + 1,
        field: "modalidad_jornada",
        type: "invalid_combination",
        value: `modalidad: ${modalidad}, jornada: ${jornada}`,
        message: `La combinación de modalidad ${modalidad} y jornada ${jornada} no es válida para Oferta Nueva`,
      });
    }
  }

  // Duration validations (same as Etapa 1)
  if (oferta.duracion_estudios && oferta.duracion_titulacion && oferta.duracion_total) {
    const de = parseInt(oferta.duracion_estudios);
    const dt = parseInt(oferta.duracion_titulacion);
    const df = parseInt(oferta.duracion_total);

    if (de > df) {
      errors.push({
        row: index + 1,
        field: "duracion_total",
        type: "duration_validation",
        message: `La duración total (${df}) no puede ser menor que la duración de estudios (${de})`,
      });
    }

    if ((de + dt) < df) {
      errors.push({
        row: index + 1,
        field: "duracion_total",
        type: "duration_validation",
        message: `La suma de duración de estudios (${de}) y duración de titulación (${dt}) debe ser mayor o igual a la duración total (${df})`,
      });
    }
  }

  // Validate nivel_global and nivel_carrera combinations
  if (oferta.cod_nivel_global && oferta.cod_nivel_carrera) {
    const nivelGlobal = parseInt(oferta.cod_nivel_global);
    const nivelCarrera = parseInt(oferta.cod_nivel_carrera);

    if (![1, 2, 3].includes(nivelGlobal)) {
      errors.push({
        row: index + 1,
        field: "cod_nivel_global",
        type: "valor_invalido",
        value: oferta.cod_nivel_global,
        message: `cod_nivel_global debe ser 1 (Pregrado), 2 (Posgrado), o 3 (Postítulo)`,
      });
    }

    if (nivelCarrera < 0 || nivelCarrera > 9) {
      errors.push({
        row: index + 1,
        field: "cod_nivel_carrera",
        type: "valor_invalido",
        value: oferta.cod_nivel_carrera,
        message: `cod_nivel_carrera debe estar entre 0 y 9 según el instructivo`,
      });
    }
  }

  // Validate required fields for new offerings
  const requiredFields = [
    'cod_sede', 'nombre_sede', 'nombre_carrera', 'modalidad', 'cod_jornada',
    'version', 'cod_tipo_plan_carrera', 'caracteristicas_tipo_plan',
    'duracion_estudios', 'duracion_titulacion', 'duracion_total',
    'regimen', 'duracion_regimen', 'nombre_titulo', 'nombre_grado',
    'cod_nivel_global', 'cod_nivel_carrera', 'anio_inicio', 'acreditacion',
    'elegible_beca_pedagogia', 'requisito_ingreso', 'semestres_reconocidos',
    'area_actual', 'vacantes_primer_semestre', 'vacantes_segundo_semestre'
  ];

  // Define numeric fields where 0 is a valid value
  const numericFieldsWithZero = [
    'elegible_beca_pedagogia',
    'duracion_titulacion',
    'semestres_reconocidos',
    'vacantes_segundo_semestre',
    'vacantes_primer_semestre',
    'vacantes_pace',
    'cod_demre',
    'ponderacion_nem',
    'ponderacion_ranking',
    'ponderacion_c_lectora',
    'ponderacion_matematicas',
    'ponderacion_matematicas_2',
    'ponderacion_historia',
    'ponderacion_ciencias',
    'ponderacion_otros'
  ];

  requiredFields.forEach(field => {
    let isEmpty = false;

    if (numericFieldsWithZero.includes(field)) {
      // For numeric fields, only consider null, undefined, or empty string as missing
      isEmpty = oferta[field] === null || oferta[field] === undefined || oferta[field] === "";
    } else {
      // For non-numeric fields, use the original logic
      isEmpty = !oferta[field] || oferta[field] === "" || oferta[field] === null || oferta[field] === undefined;
    }

    if (isEmpty) {
      errors.push({
        row: index + 1,
        field: field,
        type: "required_field",
        message: `${field} es obligatorio para Oferta Nueva`,
      });
    }
  });

  // For new programs, cod_demre should be 0
  if (oferta.cod_demre && parseInt(oferta.cod_demre) !== 0) {
    errors.push({
      row: index + 1,
      field: "cod_demre",
      type: "new_program_validation",
      value: oferta.cod_demre,
      message: `Para programas nuevos, cod_demre debe ser 0`,
    });
  }

  // Validate URL format for malla_curricular
  if (oferta.malla_curricular && !oferta.malla_curricular.match(/^https?:\/\/.+/)) {
    errors.push({
      row: index + 1,
      field: "malla_curricular",
      type: "invalid_url",
      value: oferta.malla_curricular,
      message: `malla_curricular debe ser una URL completa (http:// o https://)`,
    });
  }

  // Additional validations for Etapa 2
  // Validate ponderaciones sum (100-160%) for new programs
  const ponderacionFields = [
    'ponderacion_nem', 'ponderacion_ranking', 'ponderacion_c_lectora',
    'ponderacion_matematicas', 'ponderacion_matematicas_2',
    'ponderacion_historia', 'ponderacion_ciencias', 'ponderacion_otros'
  ];

  const ponderacionSum = ponderacionFields.reduce((sum, field) => {
    const value = parseFloat(oferta[field]) || 0;
    return sum + value;
  }, 0);

  if (ponderacionSum > 0 && (ponderacionSum < 100 || ponderacionSum > 160)) {
    errors.push({
      row: index + 1,
      field: "ponderaciones",
      type: "sum_validation",
      value: ponderacionSum,
      message: `La suma de las ponderaciones debe estar entre 100% y 160%`,
    });
  }

  // Validate that new programs have proper vigencia (should be 1 for new programs)
  if (oferta.vigencia_carrera && parseInt(oferta.vigencia_carrera) !== 1) {
    errors.push({
      row: index + 1,
      field: "vigencia_carrera",
      type: "new_program_validation",
      value: oferta.vigencia_carrera,
      message: `Para programas nuevos, vigencia_carrera debe ser 1 (Vigente con estudiantes nuevos)`,
    });
  }

  // Validate email format
  if (oferta.mail_difusion_carrera && !isValidEmail(oferta.mail_difusion_carrera)) {
    errors.push({
      row: index + 1,
      field: "mail_difusion_carrera",
      type: "invalid_email",
      value: oferta.mail_difusion_carrera,
      message: `mail_difusion_carrera debe tener un formato de email válido`,
    });
  }

  // Validate that caracteristicas_tipo_plan is properly filled
  if (oferta.cod_tipo_plan_carrera) {
    const tipoPlan = parseInt(oferta.cod_tipo_plan_carrera);
    if ([1, 3].includes(tipoPlan)) { // Plan Regular or Plan Regular de Continuidad
      if (!oferta.caracteristicas_tipo_plan || oferta.caracteristicas_tipo_plan.trim().toUpperCase() !== "NO APLICA") {
        errors.push({
          row: index + 1,
          field: "caracteristicas_tipo_plan",
          type: "plan_validation",
          message: `Para Plan Regular o Plan Regular de Continuidad, caracteristicas_tipo_plan debe ser "NO APLICA"`,
        });
      }
    }
  }
};

/**
 * Validate specific rules for Subproceso 2, Etapa 3 (Aranceles)
 * @param {Object} oferta - Single oferta object
 * @param {number} index - Index of the oferta in the array
 * @param {Array} errors - Array to push errors to
 * @param {Object} etapa - Etapa object
 */
const validateSubproceso2Etapa3 = (oferta, index, errors, etapa) => {
  // Etapa 3 focuses on financial information (aranceles)
  // Most academic fields are not modifiable, only financial fields

  // Validate formato_valor
  if (!oferta.formato_valor || ![1, 2].includes(parseInt(oferta.formato_valor))) {
    errors.push({
      row: index + 1,
      field: "formato_valor",
      type: "required_field",
      message: `formato_valor es obligatorio y debe ser 1 (Pesos chilenos) o 2 (UF)`,
    });
  }

  // Validate required financial fields
  const requiredFinancialFields = [
    'valor_matricula_anual',
    'costo_titulacion',
    'valor_certificado_diploma',
    'arancel_anual'
  ];

  requiredFinancialFields.forEach(field => {
    if (oferta[field] === undefined || oferta[field] === null || oferta[field] === "") {
      errors.push({
        row: index + 1,
        field: field,
        type: "required_field",
        message: `${field} es obligatorio para la etapa de aranceles`,
      });
    }
  });

  // Validate financial values are >= 0
  requiredFinancialFields.forEach(field => {
    if (oferta[field] !== undefined && oferta[field] !== null && oferta[field] !== "") {
      const value = parseFloat(oferta[field]);
      if (isNaN(value) || value < 0) {
        errors.push({
          row: index + 1,
          field: field,
          type: "invalid_financial_value",
          value: oferta[field],
          message: `${field} debe ser un valor numérico mayor o igual a 0`,
        });
      }
    }
  });

  // Validate arancel_anual > 0 when vigencia_carrera is 1 or 2
  if (oferta.vigencia_carrera && [1, 2].includes(parseInt(oferta.vigencia_carrera))) {
    if (!oferta.arancel_anual || parseFloat(oferta.arancel_anual) <= 0) {
      errors.push({
        row: index + 1,
        field: "arancel_anual",
        type: "required_positive_value",
        message: `Cuando vigencia_carrera es 1 o 2, arancel_anual debe ser mayor a 0`,
      });
    }
  }

  // Validate format consistency for financial fields when formato_valor = 1 (Pesos)
  if (oferta.formato_valor && parseInt(oferta.formato_valor) === 1) {
    requiredFinancialFields.forEach(field => {
      if (oferta[field] && typeof oferta[field] === 'string') {
        // Check for commas or periods in peso values
        if (oferta[field].includes(',') || oferta[field].includes('.')) {
          errors.push({
            row: index + 1,
            field: field,
            type: "format_error",
            value: oferta[field],
            message: `Cuando formato_valor es 1 (Pesos), ${field} no puede contener puntos o comas`,
          });
        }
      }
    });
  }

  // Validate format consistency for financial fields when formato_valor = 2 (UF)
  if (oferta.formato_valor && parseInt(oferta.formato_valor) === 2) {
    requiredFinancialFields.forEach(field => {
      if (oferta[field] && typeof oferta[field] === 'string') {
        // UF values can have comma for decimal
        const ufPattern = /^\d+([,]\d+)?$/;
        if (!ufPattern.test(oferta[field])) {
          errors.push({
            row: index + 1,
            field: field,
            type: "format_error",
            value: oferta[field],
            message: `Cuando formato_valor es 2 (UF), ${field} debe usar formato numérico con coma para decimales`,
          });
        }
      }
    });
  }

  // Validate acreditacion can be updated in this stage
  if (oferta.acreditacion && ![1, 2].includes(parseInt(oferta.acreditacion))) {
    errors.push({
      row: index + 1,
      field: "acreditacion",
      type: "valor_invalido",
      value: oferta.acreditacion,
      message: `acreditacion debe ser 1 (Acreditada) o 2 (No acreditada)`,
    });
  }

  // For institutions adscritas, malla_curricular and mail_difusion_carrera can be updated
  if (oferta.malla_curricular && !oferta.malla_curricular.match(/^https?:\/\/.+/)) {
    errors.push({
      row: index + 1,
      field: "malla_curricular",
      type: "invalid_url",
      value: oferta.malla_curricular,
      message: `malla_curricular debe ser una URL completa (http:// o https://)`,
    });
  }

  if (oferta.mail_difusion_carrera && !isValidEmail(oferta.mail_difusion_carrera)) {
    errors.push({
      row: index + 1,
      field: "mail_difusion_carrera",
      type: "invalid_email",
      value: oferta.mail_difusion_carrera,
      message: `mail_difusion_carrera debe tener un formato de email válido`,
    });
  }

  // Validate that non-modifiable fields are not changed (this would be checked against original data)
  // This validation would require comparing against the original data from etapas 1 and 2

  // Validate specific requirements for vigencia_carrera = 1
  if (oferta.vigencia_carrera && parseInt(oferta.vigencia_carrera) === 1) {
    if (!oferta.formato_valor) {
      errors.push({
        row: index + 1,
        field: "formato_valor",
        type: "required_field",
        message: `Cuando vigencia_carrera es 1, formato_valor es obligatorio`,
      });
    }

    if (oferta.valor_matricula_anual === undefined || oferta.valor_matricula_anual === null || oferta.valor_matricula_anual === "" || parseFloat(oferta.valor_matricula_anual) < 0) {
      errors.push({
        row: index + 1,
        field: "valor_matricula_anual",
        type: "required_field",
        message: `Cuando vigencia_carrera es 1, valor_matricula_anual debe ser mayor o igual a 0`,
      });
    }

    if (oferta.costo_titulacion === undefined || oferta.costo_titulacion === null || oferta.costo_titulacion === "" || parseFloat(oferta.costo_titulacion) < 0) {
      errors.push({
        row: index + 1,
        field: "costo_titulacion",
        type: "required_field",
        message: `Cuando vigencia_carrera es 1, costo_titulacion debe ser mayor o igual a 0`,
      });
    }

    if (oferta.valor_certificado_diploma === undefined || oferta.valor_certificado_diploma === null || oferta.valor_certificado_diploma === "" || parseFloat(oferta.valor_certificado_diploma) < 0) {
      errors.push({
        row: index + 1,
        field: "valor_certificado_diploma",
        type: "required_field",
        message: `Cuando vigencia_carrera es 1, valor_certificado_diploma debe ser mayor o igual a 0`,
      });
    }
  }

  // Validate that posgrado and postítulo programs have ponderaciones = 0
  if (oferta.cod_nivel_global && [2, 3].includes(parseInt(oferta.cod_nivel_global))) {
    const ponderacionFields = [
      'ponderacion_nem', 'ponderacion_ranking', 'ponderacion_c_lectora',
      'ponderacion_matematicas', 'ponderacion_matematicas_2',
      'ponderacion_historia', 'ponderacion_ciencias', 'ponderacion_otros'
    ];

    ponderacionFields.forEach(field => {
      if (oferta[field] && parseFloat(oferta[field]) > 0) {
        errors.push({
          row: index + 1,
          field: field,
          type: "posgrado_validation",
          value: oferta[field],
          message: `Para programas de Posgrado y Postítulo, ${field} debe ser 0`,
        });
      }
    });

    // Validate that PACE vacantes are 0 for posgrado/postítulo
    if (oferta.vacantes_pace && parseInt(oferta.vacantes_pace) !== 0) {
      errors.push({
        row: index + 1,
        field: "vacantes_pace",
        type: "posgrado_validation",
        value: oferta.vacantes_pace,
        message: `Para programas de Posgrado y Postítulo, vacantes_pace debe ser 0`,
      });
    }
  }

  // Validate area_destino fields for specific nivel_carrera
  if (oferta.cod_nivel_carrera) {
    const nivelCarrera = parseInt(oferta.cod_nivel_carrera);
    const areaDestinoFields = [
      'area_destino_agricultura', 'area_destino_ciencias', 'area_destino_cs_sociales',
      'area_destino_educacion', 'area_destino_humanidades', 'area_destino_ingenieria',
      'area_destino_salud', 'area_destino_servicios'
    ];

    // For Diplomado (5), Magíster (8), Especialidad Médica (7): area_destino should be 0
    if ([5, 7, 8].includes(nivelCarrera)) {
      areaDestinoFields.forEach(field => {
        if (oferta[field] && parseInt(oferta[field]) !== 0) {
          errors.push({
            row: index + 1,
            field: field,
            type: "area_destino_validation",
            value: oferta[field],
            message: `Cuando cod_nivel_carrera es ${nivelCarrera}, ${field} debe ser 0`,
          });
        }
      });
    }
  }

  // Validate that vigencia_carrera = 2 should not have mail_difusion_carrera
  if (oferta.vigencia_carrera && parseInt(oferta.vigencia_carrera) === 2) {
    if (oferta.mail_difusion_carrera && oferta.mail_difusion_carrera.trim() !== "") {
      errors.push({
        row: index + 1,
        field: "mail_difusion_carrera",
        type: "vigencia_validation",
        message: `Cuando vigencia_carrera es 2 (Vigente sin Alumnos Nuevos), no se debe completar mail_difusion_carrera`,
      });
    }

    if (oferta.perfil_egreso && oferta.perfil_egreso.trim() !== "") {
      errors.push({
        row: index + 1,
        field: "perfil_egreso",
        type: "vigencia_validation",
        message: `Cuando vigencia_carrera es 2 (Vigente sin Alumnos Nuevos), no se debe completar perfil_egreso`,
      });
    }
  }

  // Validate that posgrado/postítulo should not have certain fields
  if (oferta.cod_nivel_global && [2, 3].includes(parseInt(oferta.cod_nivel_global))) {
    if (oferta.otros_requisitos && oferta.otros_requisitos.trim() !== "") {
      errors.push({
        row: index + 1,
        field: "otros_requisitos",
        type: "posgrado_validation",
        message: `Para programas de Posgrado o Postítulo, no se debe completar otros_requisitos`,
      });
    }

    if (oferta.mail_difusion_carrera && oferta.mail_difusion_carrera.trim() !== "") {
      errors.push({
        row: index + 1,
        field: "mail_difusion_carrera",
        type: "posgrado_validation",
        message: `Para programas de Posgrado o Postítulo, no se debe completar mail_difusion_carrera`,
      });
    }

    if (oferta.texto_requisito_ingreso && oferta.texto_requisito_ingreso.trim() !== "") {
      errors.push({
        row: index + 1,
        field: "texto_requisito_ingreso",
        type: "posgrado_validation",
        message: `Para programas de Posgrado o Postítulo, no se debe completar texto_requisito_ingreso`,
      });
    }
  }

  // Validate caracteristicas_tipo_plan is not null
  if (!oferta.caracteristicas_tipo_plan || oferta.caracteristicas_tipo_plan.trim() === "") {
    errors.push({
      row: index + 1,
      field: "caracteristicas_tipo_plan",
      type: "required_field",
      message: `caracteristicas_tipo_plan no puede ser nulo, en caso de no corresponder completar con NO APLICA`,
    });
  }

  // Validate perfil_egreso format (comprehensive character set allowed)
  if (oferta.perfil_egreso) {
    const invalidChars = findInvalidChars(
      oferta.perfil_egreso,
      /[^A-Za-zÀ-ÖØ-öø-ÿ0-9\s.,;:()[\]{}'"¡!¿?@#$%&*+-_=<>]/g
    );
    if (invalidChars.length > 0) {
      errors.push({
        row: index + 1,
        field: "perfil_egreso",
        type: "format_error",
        value: oferta.perfil_egreso,
        invalidChars: invalidChars,
        message: `perfil_egreso contiene caracteres no válidos: "${invalidChars.join('", "')}". Solo se permiten letras, números, espacios y caracteres de puntuación básicos.`,
      });
    }
  }
};

/**
 * Validate URL
 * @param {string} url - URL to validate
 * @returns {boolean} - True if URL is valid, false otherwise
 */
const isValidURL = (url) => {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Find invalid characters in a string based on a regex pattern
 * @param {string} str - String to check
 * @param {RegExp} invalidCharsRegex - Regex pattern for invalid characters
 * @returns {string[]} - Array of unique invalid characters found
 */
const findInvalidChars = (str, invalidCharsRegex) => {
  // Find all invalid characters
  const matches = str.match(invalidCharsRegex);

  // If no invalid characters found, return empty array
  if (!matches) return [];

  // Return unique invalid characters
  return [...new Set(matches)];
};



/**
 * Export validation function for testing
 */
export { validateOfertasAcademicasData };

/**
 * Define column structures for each etapa based on official CSV templates
 * @param {number} etapaNumber - The etapa number (1, 2, or 3)
 * @returns {Array} - Array of column names for the specified etapa
 */
const getEtapaColumns = (etapaNumber) => {
  const baseColumns = [
    'cod_sede', 'nombre_sede', 'cod_carrera', 'nombre_carrera', 'modalidad',
    'cod_jornada', 'version', 'cod_tipo_plan_carrera', 'caracteristicas_tipo_plan',
    'duracion_estudios', 'duracion_titulacion', 'duracion_total', 'regimen',
    'duracion_regimen', 'nombre_titulo', 'nombre_grado', 'cod_nivel_global',
    'cod_nivel_carrera', 'cod_demre', 'anio_inicio', 'acreditacion',
    'elegible_beca_pedagogia', 'ped_med_odont_otro', 'requisito_ingreso',
    'semestres_reconocidos', 'area_actual', 'area_destino_agricultura',
    'area_destino_ciencias', 'area_destino_cs_sociales', 'area_destino_educacion',
    'area_destino_humanidades', 'area_destino_ingenieria', 'area_destino_salud',
    'area_destino_servicios', 'ponderacion_nem', 'ponderacion_ranking',
    'ponderacion_c_lectora', 'ponderacion_matematicas', 'ponderacion_matematicas_2',
    'ponderacion_historia', 'ponderacion_ciencias', 'ponderacion_otros',
    'vacantes_primer_semestre', 'vacantes_segundo_semestre', 'vacantes_pace',
    'malla_curricular', 'perfil_egreso', 'texto_requisito_ingreso',
    'otros_requisitos', 'mail_difusion_carrera', 'vigencia_carrera'
  ];

  switch (etapaNumber) {
    case 1:
      // Etapa 1: Vigente - same as base columns
      return baseColumns;

    case 2:
      // Etapa 2: Nueva - same as base columns but with ponderacion_matematicas_1 instead of ponderacion_matematicas
      return baseColumns.map(col =>
        col === 'ponderacion_matematicas' ? 'ponderacion_matematicas_1' : col
      );

    case 3:
      // Etapa 3: Aranceles - base columns with financial fields inserted before vigencia_carrera
      const etapa3Columns = [...baseColumns];
      // Remove vigencia_carrera from the end
      const vigenciaIndex = etapa3Columns.indexOf('vigencia_carrera');
      etapa3Columns.splice(vigenciaIndex, 1);

      // Add financial fields
      etapa3Columns.push(
        'formato_valor',
        'valor_matricula_anual',
        'costo_titulacion',
        'valor_certificado_diploma',
        'arancel_anual'
      );

      // Add vigencia_carrera at the end
      etapa3Columns.push('vigencia_carrera');

      return etapa3Columns;

    default:
      return baseColumns;
  }
};

/**
 * Get OAOfertaAcademicas totals by etapa number and return as CSV
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getOAOfertaAcademicasTotalsByEtapaCSV = async (req, res) => {
  try {
    const { etapaNumber } = req.params;
    const { tipo, subproceso_id } = req.query;
    const userToken = req.headers.authorization;

    // Check database connection
    await checkDatabase();

    // Validate token
    if (!(await checkTokenLocal(userToken))) {
      return res.status(401).json({
        message: "Token is not valid",
      });
    }

    // Validate required parameters
    if (!etapaNumber) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: etapaNumber",
      });
    }

    if (!subproceso_id) {
      return res.status(400).json({
        message: "Falta el parámetro requerido: subproceso_id",
      });
    }

    // Build where conditions for etapas
    const etapaWhere = {
      subproceso_id: subproceso_id,
      etapa: etapaNumber,
    };

    // Add tipo filter if provided
    if (tipo) {
      etapaWhere.tipo = tipo;
    }

    // Find etapas that match the criteria
    const etapas = await OAEtapa.findAll({
      where: etapaWhere,
    });

    if (etapas.length === 0) {
      return res.status(404).json({
        message: "No se encontraron etapas que coincidan con los criterios especificados",
      });
    }

    // Extract etapa IDs
    const etapaIds = etapas.map((etapa) => etapa.id);

    // Find all ofertas academicas for these etapas
    const ofertasAcademicas = await OAOfertaAcademica.findAll({
      where: {
        etapa_id: {
          [Op.in]: etapaIds,
        },
      },
      include: [
        {
          model: OAEtapa,
          attributes: [
            "id",
            "subproceso_id",
            "tipo",
            "etapa",
            "fecha_inicial",
            "fecha_final",
          ],
          include: [
            {
              model: OASubProceso,
              attributes: ["id", "proceso_id", "tipo"],
            },
          ],
        },
      ],
      order: [["oa_id", "ASC"]],
    });

    if (ofertasAcademicas.length === 0) {
      return res.status(404).json({
        message: "No se encontraron ofertas académicas para los criterios especificados",
      });
    }

    // Get etapa-specific columns instead of all database columns
    const headers = getEtapaColumns(parseInt(etapaNumber));

    // Prepare the data for CSV, filtering only the relevant columns
    const csvData = ofertasAcademicas.map((item) => {
      const filteredData = {};
      headers.forEach(header => {
        filteredData[header] = item.dataValues[header];
      });
      return filteredData;
    });

    // Create CSV content
    let csvContent =
      headers.map((header) => header.toUpperCase()).join(";") + "\n";
    csvContent += csvData
      .map((row) => headers.map((header) => {
        const value = row[header];
        // Handle null/undefined as empty, but preserve 0 values
        return (value === null || value === undefined) ? "" : value;
      }).join(";"))
      .join("\n");

    // Send the CSV to the front end
    const csvBuffer = iconv.encode(csvContent, "windows-1252");
    res.setHeader("Content-Type", "text/csv; charset=windows-1252");
    res.setHeader("Content-Disposition", 'attachment; filename="ofertas_academicas_totals.csv"');
    res.setHeader("Content-Length", csvBuffer.length);
    res.send(csvBuffer);
  } catch (error) {
    console.error("Error retrieving Ofertas Academicas totals:", error);
    res.status(500).json({
      message: "Error retrieving Ofertas Academicas totals",
      error: error.message,
    });
  }
};
