<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading && oa_data">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <div class='surface-card shadow-3 p-3 border-round'>
                    <!--Titulo-->
                    <div style="display: flex;align-items: center;padding-left: 1rem;">

                        <Button @click.prevent="goBack()"> <- </Button>
                                <h2 style="padding-left: 1rem;">SubProceso N°{{subproceso}} OA {{
                                    anio_proceso }}
                                </h2>
                    </div>
                    <br />
                    <TabView
                    :activeIndex='etapa_actual'
                    >
                        
<!-- Etapa 1 -->
<!-- v-if="subproceso === 2" -->
<TabPanel 
    :header="`Etapa 1`" 
    
    :disabled="etapas.find(e => e.number === 1)?.disabled"
>
    <div style="margin-left: 1rem;margin-right: 1rem;">
        <div style="display: flex;justify-content: space-between;align-items: center;">
            <Button v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                :disabled="!carga_data || getCargaData(1).some(carga => carga && carga.validado)"
                @click="handleValidate(1)">Validar</Button>
        </div>
        
        <!-- DataTable for Insumos -->
        <h3>Insumos</h3>

        <!-- se puede agregar un synthetic row para los consolidados-->
        <div class="flex justify-content-between align-items-center">
            <DataTable
                :value="subproceso === 1 ? getInsumosData(1) : insumosWithTotals"
                :sortOrder="-1"
                :sortField="'categoria'"
                showGridlines
                scrollable
                style="width: 100%;"
            >
                <Column
                    v-if="subproceso === 2"
                    header="Categoria"
                    field="categoria"
                    style="width: 10%"
                    class="center-header"
                />

                <Column header="N° OAs" field="carrerasCount" style="width: 10%" class="center-header" />
                <!-- hacer etapas para cada postgrado post titulo y pregrado y despues ver como obtener y actualizar la data -->
                <Column header="Fecha carga" field="fecha_carga" dateFormat="dd/mm/yy" style="width: 10%" class="center-header">
                    <template #body="slotProps">
                        <span v-if="slotProps.data.isTotal">-</span>
                        <span v-else>{{ slotProps.data.fecha_carga ? new Date(slotProps.data.fecha_carga).toLocaleDateString() : 'Sin Datos' }}</span>
                    </template>
                </Column>

                <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                    field="Cargar Datos" header="Cargar Datos" style="width: 5%" class="center-header">
                    <template #body="slotProps">
                        <Button
                            icon="pi pi-upload"
                            class="p-button-text"
                            @click="handleUpload(1, 'Insumos', slotProps.data.categoria)"
                            :disabled="slotProps.data.isTotal ? getCargaData(1).some(carga => carga.validado) : getCargaData(1).some(carga => carga.validado)"
                        />
                    </template>
                </Column>
                
                <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                    field="Exportar" header="Exportar" style="width: 5%" class="center-header">
                    <template #body="slotProps">
                        <Button v-if="slotProps.data.carrerasCount > 0"
                            icon="pi pi-download"
                            class="p-button-text"
                            @click="handleExport(1, 'Insumos', slotProps.data.categoria)"
                        />
                        <span v-else>Sin Datos</span>
                    </template>
                </Column>
                
                <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                    field="eliminar" header="Eliminar" style="width: 5%" class="center-header">
                    <template #body="slotProps">
                        <Button icon="pi pi-trash"
                            class="p-button-text"
                            @click="handleDelete(1, 'Insumos', slotProps.data.categoria)"
                            :disabled="slotProps.data.isTotal ? getCargaData(1).some(carga => carga.validado) : getCargaData(1).some(carga => carga.validado)"
                        />
                    </template>
                </Column>
            </DataTable>
        </div>

        <h3 class="mt-4">Carga</h3>
        <DataTable
            :value="subproceso === 2 ? cargaWithTotals : getCargaData(1)"
            :sortOrder="-1"
            :sortField="subproceso === 2 ? 'categoria' : undefined"
            showGridlines
            scrollable
            style="width: 100%;"
        >
            <Column
                v-if="subproceso === 2"
                header="Categoria"
                field="categoria"
                style="width: 10%"
                class="center-header"
            />

            <Column header="Subido por" field="subido_por" style="width: 10%" class="center-header" />
            <Column header="N° OAs" field="carrerasCount" style="width: 10%" class="center-header" />

            <Column field="validado" header="Estado" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <span v-if="slotProps.data.isTotal">-</span>
                    <div v-else-if="!slotProps.data.validado" v-tooltip.bottom="pending_text">
                        <Button disabled icon="pi pi-hourglass" class="p-button-text" style="color: orangered;" />
                    </div>
                    <div v-else v-tooltip.bottom="finalized_text">
                        <Button disabled icon="pi pi-verified" class="p-button-text green-icon" style="color: green;" />
                    </div>
                </template>
            </Column>

            <Column header="Fecha inicio" field="fecha_inicial" style="width: 10%" class="center-header">
                <template #body="slotProps">
                    <span v-if="slotProps.data.isTotal">-</span>
                    <span v-else>{{ slotProps.data.fecha_inicial ? new Date(slotProps.data.fecha_inicial).toLocaleDateString() : 'Sin Datos' }}</span>
                </template>
            </Column>

            <Column header="Fecha límite" field="fecha_final" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <span v-if="slotProps.data.isTotal">-</span>
                    <span v-else>{{ slotProps.data.fecha_final ? new Date(slotProps.data.fecha_final).toLocaleDateString() : 'Sin Datos' }}</span>
                </template>
            </Column>

            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="Cargar Datos" header="Cargar Datos" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button
                        icon="pi pi-upload"
                        class="p-button-text"
                        @click="handleUpload(1, 'Carga', slotProps.data.categoria)"
                        :disabled="slotProps.data.isTotal ? getCargaData(1).some(carga => carga.validado) : slotProps.data.validado"
                    />
                </template>
            </Column>

            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="Exportar" header="Exportar" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button v-if="slotProps.data.carrerasCount > 0"
                        icon="pi pi-download"
                        class="p-button-text"
                        @click="handleExport(1, 'Carga', slotProps.data.categoria)"
                    />
                    <span v-else>Sin Datos</span>
                </template>
            </Column>

            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="eliminar" header="Eliminar" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button icon="pi pi-trash"
                        class="p-button-text"
                        @click="handleDelete(1, 'Carga', slotProps.data.categoria)"
                        :disabled="slotProps.data.isTotal ? getCargaData(1).some(carga => carga.validado) : slotProps.data.validado"
                    />
                </template>
            </Column>
        </DataTable>
    </div>
    <br />
</TabPanel>


<!-- Etapa 2 -->
<TabPanel
    :header="`Etapa 2`"
    :disabled="etapas.find(e => e.number === 2)?.disabled"
>
    <div style="margin-left: 1rem;margin-right: 1rem;">
        <div style="display: flex;justify-content: space-between;align-items: center;">
            <Button v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                :disabled="getCargaData(2).some(carga => carga.validado)"
                @click="handleValidate(2)">Validar</Button>
        </div>

        <h3 class="mt-4">Carga</h3>
        <DataTable
            :value="subproceso === 1 ? getCargaData(2) : cargaEtapa2WithTotals"
            :sortOrder="-1"
            :sortField="subproceso === 1 ? undefined : 'categoria'"
            showGridlines
            scrollable
            style="width: 100%;"
        >
            <Column
                header="Categoria"
                field="categoria"
                style="width: 10%"
                class="center-header"
            />

            <Column header="Subido por" field="subido_por" style="width: 10%" class="center-header" />
            <Column header="N° OAs" field="carrerasCount" style="width: 10%" class="center-header" />

            <Column field="validado" header="Estado" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <span v-if="slotProps.data.isTotal">-</span>
                    <div v-else-if="!slotProps.data.validado" v-tooltip.bottom="pending_text">
                        <Button disabled icon="pi pi-hourglass" class="p-button-text" style="color: orangered;" />
                    </div>
                    <div v-else v-tooltip.bottom="finalized_text">
                        <Button disabled icon="pi pi-verified" class="p-button-text green-icon" style="color: green;" />
                    </div>
                </template>
            </Column>

            <Column header="Fecha inicio" field="fecha_inicial" style="width: 10%" class="center-header">
                <template #body="slotProps">
                    <span v-if="slotProps.data.isTotal">-</span>
                    <span v-else>{{ slotProps.data.fecha_inicial ? new Date(slotProps.data.fecha_inicial).toLocaleDateString() : 'Sin Datos' }}</span>
                </template>
            </Column>

            <Column header="Fecha límite" field="fecha_final" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <span v-if="slotProps.data.isTotal">-</span>
                    <span v-else>{{ slotProps.data.fecha_final ? new Date(slotProps.data.fecha_final).toLocaleDateString() : 'Sin Datos' }}</span>
                </template>
            </Column>

            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="Cargar Datos" header="Cargar Datos" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button
                        icon="pi pi-upload"
                        class="p-button-text"
                        @click="handleUpload(2, 'Carga', slotProps.data.categoria)"
                        :disabled="slotProps.data.isTotal ? getCargaData(2).some(carga => carga.validado) : slotProps.data.validado"
                    />
                </template>
            </Column>

            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="Exportar" header="Exportar" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button v-if="slotProps.data.carrerasCount > 0"
                        icon="pi pi-download"
                        class="p-button-text"
                        @click="handleExport(2, 'Carga', slotProps.data.categoria)"
                    />
                    <span v-else>Sin Datos</span>
                </template>
            </Column>

            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="eliminar" header="Eliminar" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button icon="pi pi-trash"
                        class="p-button-text"
                        @click="handleDelete(2, 'Carga', slotProps.data.categoria)"
                        :disabled="slotProps.data.isTotal ? getCargaData(2).some(carga => carga.validado) : slotProps.data.validado"
                    />
                </template>
            </Column>
        </DataTable>
    </div>
    <br />
</TabPanel>
<TabPanel
    v-if="subproceso === 2"
    :header="`Etapa 3`"
    :disabled="etapas.find(e => e.number === 3)?.disabled"
>
    <div style="margin-left: 1rem;margin-right: 1rem;">
        <div style="display: flex;justify-content: space-between;align-items: center;">
            <Button v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                :disabled="getCargaData(3).some(carga => carga.validado)"
                @click="handleValidate(3)">Validar</Button>
        </div>

        <h3 class="mt-4">Carga</h3>
        <DataTable
            :value="cargaEtapa3WithTotals"
            :sortOrder="-1"
            :sortField="'categoria'"
            showGridlines
            scrollable
            style="width: 100%;"
        >
            <Column
                header="Categoria"
                field="categoria"
                style="width: 10%"
                class="center-header"
            />

            <Column header="Subido por" field="subido_por" style="width: 10%" class="center-header" />
            <Column header="N° OAs" field="carrerasCount" style="width: 10%" class="center-header" />

            <Column field="validado" header="Estado" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <span v-if="slotProps.data.isTotal">-</span>
                    <div v-else-if="!slotProps.data.validado" v-tooltip.bottom="pending_text">
                        <Button disabled icon="pi pi-hourglass" class="p-button-text" style="color: orangered;" />
                    </div>
                    <div v-else v-tooltip.bottom="finalized_text">
                        <Button disabled icon="pi pi-verified" class="p-button-text green-icon" style="color: green;" />
                    </div>
                </template>
            </Column>

            <Column header="Fecha inicio" field="fecha_inicial" style="width: 10%" class="center-header">
                <template #body="slotProps">
                    <span v-if="slotProps.data.isTotal">-</span>
                    <span v-else>{{ slotProps.data.fecha_inicial ? new Date(slotProps.data.fecha_inicial).toLocaleDateString() : 'Sin Datos' }}</span>
                </template>
            </Column>

            <Column header="Fecha límite" field="fecha_final" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <span v-if="slotProps.data.isTotal">-</span>
                    <span v-else>{{ slotProps.data.fecha_final ? new Date(slotProps.data.fecha_final).toLocaleDateString() : 'Sin Datos' }}</span>
                </template>
            </Column>

            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="Cargar Datos" header="Cargar Datos" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button
                        icon="pi pi-upload"
                        class="p-button-text"
                        @click="handleUpload(3, 'Carga', slotProps.data.categoria)"
                        :disabled="slotProps.data.isTotal ? getCargaData(3).some(carga => carga.validado) : slotProps.data.validado"
                    />
                </template>
            </Column>

            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="Exportar" header="Exportar" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button v-if="slotProps.data.carrerasCount > 0"
                        icon="pi pi-download"
                        class="p-button-text"
                        @click="handleExport(3, 'Carga', slotProps.data.categoria)"
                    />
                    <span v-else>Sin Datos</span>
                </template>
            </Column>

            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="eliminar" header="Eliminar" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button icon="pi pi-trash"
                        class="p-button-text"
                        @click="handleDelete(3, 'Carga', slotProps.data.categoria)"
                        :disabled="slotProps.data.isTotal ? getCargaData(3).some(carga => carga.validado) : slotProps.data.validado"
                    />
                </template>
            </Column>
        </DataTable>
    </div>
    <br />
</TabPanel>
                    </TabView>
                </div>
            </div>

            <Dialog v-model:visible="dialogVisibleEliminar" modal :header="`Eliminar ${currentTipoForDialog}`" :style="{ width: '25rem' }"
                v-on:hide="resetForm()">
                <div v-if="dialogIsLoading && !muPregradoisCorrect">
                    <Loading />
                </div>

                <div v-if="!dialogIsLoading && !muPregradoisCorrect">
                    <div>
                        <!-- Validation error message -->
                        <p>Esto eliminará permanentemente este registro ¿Está seguro?</p>
                    </div>
                    <br />
                    <div class="flex justify-content-center gap-2">
                        <InputSwitch :disabled="dialogHasError" style="scale: 1.5;" v-model="deletePreConfirmation">
                        </InputSwitch>
                    </div>
                    <br />
                    <!-- Validation error message -->
                    <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}<br /></div>


                    <!-- Buttons -->
                    <div class="flex justify-content-end gap-2">
                        <Button type="button" label="Cancelar" severity="secondary"
                            @click="dialogVisibleEliminar = false"></Button>
                        <Button :disabled="dialogHasError" v-if="deletePreConfirmation" type="button" label="Eliminar"
                            @click="eliminarOfertasAcademicas()"></Button>
                    </div>
                </div>

                <!-- Success message -->
                <div v-if="!dialogIsLoading && muPregradoisCorrect"
                    style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="pi pi-trash" style="font-size: 5rem; color: blue;"></i>
                    <br />
                    <span class="p-text-secondary block mb-5">¡Eliminado correctamente!</span>
                </div>

            </Dialog>
            <Dialog v-model:visible="dialogConfirmarMU_post" modal header="Finalizar Etapa"
                :style="{ width: '25rem' }" v-on:hide="resetForm()">
                <div v-if="dialogIsLoading && !muPregradoisCorrect">
                    <Loading />
                </div>

                <div v-if="!dialogIsLoading && !muPregradoisCorrect">
                    <div>
                        <p class="block mb-3">Al validar el registro este no se podrá editar ni eliminar ¿Está
                            seguro?
                        </p>
                        <p class="p-error block mb-3">Esta acción es IRREVERSIBLE.
                        </p>
                    </div>
                    <br />
                    <div class="flex justify-content-center gap-2">
                        <InputSwitch style="scale: 1.5;" v-model="validatePreConfirmation"></InputSwitch>
                    </div>
                    <br />
                    <div>
                        <!-- Validation error message -->
                        <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}</div>
 
                        <br />
                    </div>
                    <!-- Buttons -->
                    <div class="flex justify-content-end gap-2">
                        <Button type="button" label="Cancelar" severity="secondary"
                            @click="dialogConfirmarMU = false"></Button>
                        <Button v-if="validatePreConfirmation" type="button" label="Confirmar"
                            @click="validateOAEtapa(currentEtapaForDialog)"></Button>
                    </div>
                </div>

                <!-- Success message -->
                <div v-if="!dialogIsLoading && muPregradoisCorrect"
                    style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="pi pi-check" style="font-size: 5rem; color: blue;"></i>
                    <br />
                    <span class="p-text-secondary block mb-5">¡Registro validado correctamente!</span>
                </div>

            </Dialog>

            <Dialog v-model:visible="dialogCargarDatosisVisible" modal :header="dialogHeader"
                :style="{ width: '25rem' }" v-on:hide="resetForm()">

                <div v-show="!cargarDatosIsLoading && !cargarDatosHasError && !cargarDatosisCorrect" class="card">
                    <FileUpload ref="fileUploadRef" name="estudianteData" uploadLabel="Cargar"
                        v-on:select="onSelect($event)" v-on:before-upload="onUpload()" :multiple="false" accept=".csv"
                        :maxFileSize="1000000" :fileLimit=1>
                        <template #empty>
                            <p>Arrastra y suelta un archivo aquí para subirlo.</p>
                        </template>
                    </FileUpload>
                </div>
                <div v-if="!cargarDatosIsLoading && cargarDatosisCorrect" class="flex flex-column align-items-center">
                    <i class="pi pi-check-circle" style="font-size: 3rem; color: green;"></i>
                    <p class="text-center mt-3">El archivo se cargó correctamente.</p>
                </div>

                <div v-if="!cargarDatosIsLoading && cargarDatosHasError" class="flex flex-column align-items-center">
                    <i class="pi pi-exclamation-triangle" style="font-size: 3rem; color: red;"></i>
                    <p class="text-center mt-3">Se encontraron errores en el archivo.</p>
                    <Button v-if="errorData && errorData.length > 0"
                            label="Descargar errores"
                            icon="pi pi-download"
                            class="p-button-danger mt-3"
                            @click="downloadErrors" />
                </div>
                    <!-- Only show date fields if creating a new etapa (not uploading to an existing one) -->
                    <div v-if="!isExistingEtapa">
                        <!-- For Insumos, show fecha_inicio and fecha_final -->
                        <div v-if="currentTipoForDialog === 'Insumos'" class="mt-3">
                            <div class="mb-2">
                                <label for="fechaInicio">Fecha inicio: </label>
                                <Calendar id="fecha_inicio" v-model="fecha_inicio" dateFormat="dd/mm/yy" :minDate="new Date()" />
                            </div>
                            <div>
                                <label for="fechaFinal">Fecha final: </label>
                                <Calendar id="fecha_final" v-model="fecha_final" dateFormat="dd/mm/yy" :minDate="fecha_inicio || new Date()" />
                            </div>
                        </div>

                        <!-- For Carga, show fecha_inicio and fecha_final -->
                        <div v-if="currentTipoForDialog === 'Carga'" class="mt-3">
                            <div class="mb-2">
                                <label for="fechaInicio">Fecha inicio: </label>
                                <Calendar id="fecha_inicio" v-model="fecha_inicio" dateFormat="dd/mm/yy" :minDate="new Date()" />
                            </div>
                            <div>
                                <label for="fechaFinal">Fecha final: </label>
                                <Calendar id="fecha_final" v-model="fecha_final" dateFormat="dd/mm/yy" :minDate="fecha_inicio || new Date()" />
                            </div>
                        </div>
                    </div>
                    <div v-else class="mt-3">
                        <!-- <p class="text-info">Usando fechas de la etapa existente</p> -->
                    </div>
                <div v-if="cargarDatosIsLoading">
                    <Loading />
                </div>

            </Dialog>

        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
        <Toast position="bottom-left" group="bl" />
    </div>



</template>

<script>
import { useAuthStore } from '../../store/auth';
import { ref, computed, onMounted,nextTick  } from 'vue';
import { decryptData } from '@/utils/crypto';
import axios from 'axios';
import Papa from 'papaparse';
import { useToast } from 'primevue/usetoast';
// import EtapaContent from '../../components/oferta_academica/EtapaContent.vue';



export default {

    props: {
        subproceso_id: {
            type: String,
            required: true
        },
        anio_proceso: {
            type: String,
            required: true
        }
    },
    setup(props) {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const authStore = useAuthStore();
        const toast = useToast();
        const isLoading = ref(false);
        const oa_data = ref([]);
        const carga_data = ref([]);
        const insumos_data = ref([]);
        const etapa2_insumos_data = ref([]);
        const etapa2_carga_data = ref([]);
        const dialogVisibleEliminar = ref(false);
        const dialogIsLoading = ref(false);
        const finalized_text = ref("Finalizado");
        const pending_text = ref("Pendiente");
        const fecha_termino = ref();
        const fecha_inicio = ref();
        const fecha_final = ref();
        // Obtain the stored user data from pinia and the global loading state
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));
        const etapa_actual = ref(0);
        // Check if props.subproceso_id and props.anio are defined before decrypting
        const subproceso_id = computed(() => props.subproceso_id ? decryptData(props.subproceso_id) : null);
        const subproceso = ref();// computed(() => props.subproceso ? decryptData(props.subproceso) : null);
        const anio_proceso = computed(() => props.anio_proceso ? decryptData(props.anio_proceso) : null);
        const muPregradoisCorrect = ref(false);
        const errorMessage = ref('');
        const deletePreConfirmation = ref(false);
        const dialogConfirmarMU_post = ref(false);
        const validatePreConfirmation = ref(false);
        const muPregradoAnioSelecionado = ref({});
        

        const errorData = ref([]);
        const errorCSVContent = ref('');

        //Dialog cargar datos
        const cargarDatosHasError = ref(false);
        const cargarDatosisCorrect = ref(false);
        const dialogCargarDatosisVisible = ref(false);
        const cargarDatosIsLoading = ref(false);
        const fileUploadRef = ref(null);
        const parsedData = ref([]);
        const rawCsvContent = ref(''); // Store the raw CSV content
        const currentEtapaForDialog = ref(null); // Etapa seleccionada (varia segun etapa) para la ventana de dialogo
        const currentTipoForDialog = ref(null);
        const currentCategoriaForDialog = ref(null);
        const isExistingEtapa = ref(false); // Flag to indicate if we're uploading to an existing etapa
        const dialogHeader = computed(() => {
            if (currentTipoForDialog.value === 'Insumos') {
                return 'Cargar insumo OAs';
            } else if (currentTipoForDialog.value === 'Carga') {
                return 'Cargar datos OAs';
            }
            return 'Cargar OAs';
        });
        // Define available etapas
        const etapas = ref([
            { number: 1 },
            { number: 2 },
            { number: 3 },
        ]);

        // Define constants for tipo values
        const TIPO_INSUMO = 'insumo';
        const TIPO_CARGA = 'carga';

        const hasAccess = (allowedRol, requiredRecurso) => {
            const access = permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
            if (access) {

                return true;
            } else {

                return false;
            }

        };
        const goBack = () => {
            window.history.back();
            // router.push({ name: "oferta-academica-sies-subprocesos", params: {subproceso_id: subproceso_id, anio_proceso: anio_proceso } });
        };//no esta funcionando
        onMounted(async () => {
            try {
                isLoading.value = true;

                // Validate page state first
                validatePageState();

                // Load data with proper error handling
                await getEtapas();
                await getEtapaActual();

                // Determine the earliest etapa that isn't validated
                await getDisabledEtapas();

                // Wait for DOM updates
                await nextTick();

                console.log('Component mounted successfully. Current etapa:', etapa_actual.value);

            } catch (error) {
                console.error('Error during component mounting:', error);

                // Ensure page remains functional even if mounting fails
                isLoading.value = false;
                validatePageState();

                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    group: 'bl',
                    detail: 'Error al cargar la página. Algunas funciones pueden no estar disponibles.',
                    life: 5000
                });
            } finally {
                isLoading.value = false;
            }
        });
        const getDisabledEtapas = () => {
          const earliestUnvalidatedEtapa = etapas.value.find(etapa => {
                const etapaData = carga_data.value.find(item => item.etapa === etapa.number);
                return !etapaData?.validado;
            });
            // Disable etapas that are not the current one
            etapas.value.forEach(etapa => {
                const etapaData = carga_data.value.find(item => item.etapa === etapa.number);
                etapa.disabled = earliestUnvalidatedEtapa ? (etapa.number !== earliestUnvalidatedEtapa.number && !etapaData?.validado) : false;
            });
            console.log('etapas: ', etapas.value);
            // Get the index of the first unvalidated etapa, or 0 if none found
            // etapa_actual.value = earliestUnvalidatedEtapa ? etapas.value.indexOf(earliestUnvalidatedEtapa) : 0;
            isLoading.value = false;
        };
        const getEtapaActual = async () => {
            try {
                isLoading.value = true;
                const response = await axios.get(API_BASE_URL + `oa_subproceso/${subproceso_id.value}`, {

                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });

                if (response.status == 200) {
                    console.log('response.data: ', response.data);
                    subproceso.value = response.data.tipo;
                    etapa_actual.value = response.data.etapa_actual-1;
                    console.log('etapa_actual: ', etapa_actual.value);
                }
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        };
        const getEtapas = async () => {
            try {
                isLoading.value = true;

                // Validate required data before making request
                if (!subproceso_id.value) {
                    throw new Error('ID de subproceso no válido');
                }

                if (!userToken.value) {
                    throw new Error('Token de autenticación no válido');
                }

                const response = await axios.get(API_BASE_URL + `oa_etapas/subproceso/${subproceso_id.value}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    },
                    timeout: 15000 // 15 second timeout
                });

                if (response.status === 200) {
                    // Validate response data structure
                    if (!response.data) {
                        throw new Error('Respuesta del servidor vacía');
                    }

                    // Store the full response data with proper validation
                    const responseData = response.data.data || response.data;
                    oa_data.value = Array.isArray(responseData) ? responseData : [responseData];

                    // Filter data with error handling
                    try {
                        insumos_data.value = oa_data.value.filter(item =>
                            item && item.tipo === TIPO_INSUMO
                        );

                        carga_data.value = oa_data.value.filter(item =>
                            item && item.tipo === TIPO_CARGA
                        );

                        console.log('Data loaded successfully:', {
                            total: oa_data.value.length,
                            insumos: insumos_data.value.length,
                            carga: carga_data.value.length
                        });

                    } catch (filterError) {
                        console.error('Error filtering data:', filterError);
                        // Set empty arrays as fallback
                        insumos_data.value = [];
                        carga_data.value = [];
                    }
                } else {
                    throw new Error(`Error del servidor: ${response.status}`);
                }
            } catch (error) {
                console.error("Error loading etapas:", error);

                // Set empty data as fallback
                oa_data.value = [];
                insumos_data.value = [];
                carga_data.value = [];

                // Show user-friendly error message
                let errorMessage = "Error al cargar los datos de etapas";

                if (error.response) {
                    if (error.response.status === 401) {
                        errorMessage = "Sesión expirada. Por favor, inicie sesión nuevamente";
                    } else if (error.response.status === 403) {
                        errorMessage = "No tiene permisos para acceder a estos datos";
                    } else if (error.response.status === 404) {
                        errorMessage = "No se encontraron datos para este subproceso";
                    } else if (error.response.status >= 500) {
                        errorMessage = "Error interno del servidor. Intente más tarde";
                    }
                } else if (error.code === 'ECONNABORTED') {
                    errorMessage = "Tiempo de espera agotado. Verifique su conexión";
                } else if (error.request) {
                    errorMessage = "Error de conexión. Verifique su conexión a internet";
                } else if (error.message) {
                    errorMessage = error.message;
                }

                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    group: 'bl',
                    detail: errorMessage,
                    life: 5000
                });
            } finally {
                isLoading.value = false;
            }
        };

        // Function to validate page state and ensure functionality
        const validatePageState = () => {
            try {
                // Ensure critical reactive values are properly initialized
                if (oa_data.value === null || oa_data.value === undefined) {
                    oa_data.value = [];
                }
                if (carga_data.value === null || carga_data.value === undefined) {
                    carga_data.value = [];
                }
                if (insumos_data.value === null || insumos_data.value === undefined) {
                    insumos_data.value = [];
                }

                // Ensure dialog states are boolean
                if (typeof dialogCargarDatosisVisible.value !== 'boolean') {
                    dialogCargarDatosisVisible.value = false;
                }
                if (typeof dialogVisibleEliminar.value !== 'boolean') {
                    dialogVisibleEliminar.value = false;
                }
                if (typeof dialogConfirmarMU_post.value !== 'boolean') {
                    dialogConfirmarMU_post.value = false;
                }

                // Ensure loading states are boolean
                if (typeof cargarDatosIsLoading.value !== 'boolean') {
                    cargarDatosIsLoading.value = false;
                }
                if (typeof isLoading.value !== 'boolean') {
                    isLoading.value = false;
                }

                return true;
            } catch (error) {
                console.error('Error validating page state:', error);
                return false;
            }
        };

        // Function to reset the form for a new creation
        const resetForm = () => {
            try {
                errorMessage.value = ''; // Clear any error messages
                muPregradoisCorrect.value = false; // Reset success state
                deletePreConfirmation.value = false;
                dialogVisibleEliminar.value = false;
                currentEtapaForDialog.value = null; // Reset currentEtapa to null
                currentTipoForDialog.value = null;
                fecha_inicio.value = null; // Reset fecha_inicio
                fecha_final.value = null; // Reset fecha_final
                isExistingEtapa.value = false; // Reset the existing etapa flag
                rawCsvContent.value = ''; // Reset the raw CSV content
                dialogConfirmarMU_post.value = false;
                cargarDatosHasError.value = false;
                cargarDatosIsLoading.value = false;
                cargarDatosisCorrect.value = false;
                validatePreConfirmation.value = false;

                // Clear file upload with error handling
                try {
                    if (fileUploadRef.value && typeof fileUploadRef.value.clear === 'function') {
                        fileUploadRef.value.clear();
                    }
                } catch (clearError) {
                    console.warn('Error clearing file upload:', clearError);
                }

                // Reset parsed data
                parsedData.value = [];

                // Refresh disabled etapas state
                try {
                    getDisabledEtapas();
                } catch (disabledError) {
                    console.warn('Error updating disabled etapas:', disabledError);
                }

                // Validate overall page state
                validatePageState();

            } catch (error) {
                console.error('Error in resetForm:', error);
                // Ensure critical states are reset even if there's an error
                cargarDatosIsLoading.value = false;
                dialogCargarDatosisVisible.value = false;
                dialogVisibleEliminar.value = false;
                dialogConfirmarMU_post.value = false;
            }
        }

        const validarMatriculaUnificada = async () => {
            try {
                dialogIsLoading.value = true;

                const response = await axios.get(API_BASE_URL + `oacademica/${anio_proceso}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                muPregradoAnioSelecionado.value = await response.data;
                if (muPregradoAnioSelecionado.value.is_finalized == true) {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                } else {
                    console.log(muPregradoAnioSelecionado.value.estudiante_count);
                    if (

                        muPregradoAnioSelecionado.value.estudiante_count >= 0
                    ) {
                        try {
                            const body = ref({});
                            body.value.anio_proceso = muPregradoAnioSelecionado.value.anio_proceso;
                            const response2 = await axios.patch(API_BASE_URL + `oacademica/${anio_proceso}`, body.value, {
                                headers: {
                                    Authorization: `Bearer ${userToken.value}`
                                }
                            });
                            // Check if the response is successful (status code 200)
                            if (response2.status == 201) {
                                muPregradoisCorrect.value = true;
                                isLoading.value = true;
                                // await getOfertaAcademicaData();
                                await getEtapas();
                                isLoading.value = false;
                            }
                        } catch (error) {
                            resetForm();
                            toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Se detectó un error, inténtelo más tarde.', life: 5000 });

                        }

                    } else {
                        // La infraestructura recurso esta vacia (sin inmuebles)
                        resetForm();
                        toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'La infraestructura debe tener inmuebles asociados.', life: 5000 });
                    }
                }

            }
            catch (error) {
                console.error("Error fetching data:", error);
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrió un error, inténtalo más tarde', life: 5000 });
            }
            finally {
                dialogIsLoading.value = false;
            }
        };

        const eliminarOfertasAcademicas = async () => {
            try {
                dialogIsLoading.value = true;
                let etapa_id;
                
                    // If we're using an existing etapa, find its ID from the current data
                    if (currentCategoriaForDialog.value === 'Total') {
                        // Eliminar todos los registros de la misma etapa y tipo (sin importar la categoría)
                        const itemsToDelete = oa_data.value.filter(
                            item => item.etapa === currentEtapaForDialog.value &&
                                item.tipo === (currentTipoForDialog.value === 'Insumos' ? TIPO_INSUMO : TIPO_CARGA)
                        );
                        if (itemsToDelete.length === 0) {
                            throw new Error("No se encontraron registros para eliminar en la etapa seleccionada.");
                        }
                        // Eliminar todos los registros encontrados
                        for (const item of itemsToDelete) {
                            await axios.delete(
                                `${API_BASE_URL}oa_oferta_academica/etapa/${item.id}`,
                                {
                                    headers: {
                                        Authorization: `Bearer ${userToken.value}`,
                                    },
                                }
                            );
                        }
                        dialogVisibleEliminar.value = false;
                        await getEtapas();
                        toast.add({
                            severity: "success",
                            summary: "Éxito",
                            group: "bl",
                            detail: `Todos los registros de la etapa ${currentEtapaForDialog.value} eliminados correctamente.`,
                            life: 5000,
                        });
                        return;
                    }
                    const existingEtapa = oa_data.value.find(
                        item => item.etapa === currentEtapaForDialog.value &&
                              item.tipo === (currentTipoForDialog.value === 'Insumos' ? TIPO_INSUMO : TIPO_CARGA)
                            && item.categoria === currentCategoriaForDialog.value
                    );
                    console.log('existingEtapa: ', existingEtapa);

                    if (existingEtapa) {
                        etapa_id = existingEtapa.id;
                    } else {
                        throw new Error("Could not find the existing etapa");
                    }
                const response = await axios.delete(
                    `${API_BASE_URL}oa_oferta_academica/etapa/${etapa_id}`,
                    {
                        headers: {
                            Authorization: `Bearer ${userToken.value}`,
                        },
                    }
                );

                if (response.status === 200) {
                    toast.add({
                        severity: "success",
                        summary: "Éxito",
                        group: "bl",
                        detail: `${currentTipoForDialog.value} eliminado correctamente.`,
                        life: 5000,
                    });
                    dialogVisibleEliminar.value = false;
                    await getEtapas();
                } else {
                    toast.add({
                        severity: "error",
                        summary: "Error",
                        group: "bl",
                        detail: "Ocurrió un error al eliminar el registro.",
                        life: 5000,
                    });
                }
            } catch (error) {
                console.error("Error deleting record:", error);
                toast.add({
                    severity: "error",
                    summary: "Error",
                    group: "bl",
                    detail: "Ocurrió un error al eliminar el registro.",
                    life: 5000,
                });
            } finally {
                dialogIsLoading.value = false;
            }
        };
        const onSelect = async (event) => {
            try {
                cargarDatosIsLoading.value = true;
                cargarDatosHasError.value = false;
                cargarDatosisCorrect.value = false;

                // Validate file selection
                if (!event.files || event.files.length === 0) {
                    throw new Error('No se seleccionó ningún archivo');
                }

                const file = event.files[0];

                // Validate file type
                if (!file.name.toLowerCase().endsWith('.csv')) {
                    throw new Error('El archivo debe ser de tipo CSV (.csv)');
                }

                // Validate file size (max 10MB)
                const maxSize = 10 * 1024 * 1024; // 10MB in bytes
                if (file.size > maxSize) {
                    throw new Error('El archivo es demasiado grande. Tamaño máximo: 10MB');
                }

                // Validate file is not empty
                if (file.size === 0) {
                    throw new Error('El archivo está vacío');
                }

                const reader = new FileReader();

                // Handle file reading errors
                reader.onerror = () => {
                    throw new Error('Error al leer el archivo');
                };

                // Read the file as text with Windows-1252 encoding
                reader.readAsText(file, 'windows-1252');
                reader.onload = async (e) => {
                    try {
                        const fileContent = e.target.result;

                        // Validate file content is not empty
                        if (!fileContent || fileContent.trim().length === 0) {
                            throw new Error('El archivo está vacío o no contiene datos válidos');
                        }

                        // Store the raw CSV content
                        rawCsvContent.value = fileContent;
                    // Check if the headers match the required format
                    console.log('subproceso.value', subproceso.value);
                    // let requiredHeaders = [];
                    // if (subproceso.value === 1) {
                        
                    //      requiredHeaders = [
                    //         'COD_SEDE', 'NOMBRE_SEDE', 'COD_CARRERA', 'NOMBRE_CARRERA', 'MODALIDAD', 
                    //         'COD_JORNADA', 'VERSION', 'COD_TIPO_PLAN_CARRERA', 'CARACTERISTICAS_TIPO_PLAN', 
                    //         'DURACION_ESTUDIOS', 'DURACION_TITULACION', 'DURACION_TOTAL', 'REGIMEN', 
                    //         'DURACION_REGIMEN', 'NOMBRE_TITULO', 'NOMBRE_GRADO', 'COD_NIVEL_GLOBAL', 
                    //         'COD_NIVEL_CARRERA', 'COD_DEMRE', 'ANIO_INICIO', 'ACREDITACION', 
                    //         'ELEGIBLE_BECA_PEDAGOGIA', 'PED_MED_ODONT_OTRO', 'REQUISITO_INGRESO', 
                    //         'SEMESTRES_RECONOCIDOS', 'AREA_ACTUAL', 'AREA_DESTINO_AGRICULTURA', 
                    //         'AREA_DESTINO_CIENCIAS', 'AREA_DESTINO_CS_SOCIALES', 'AREA_DESTINO_EDUCACION', 
                    //         'AREA_DESTINO_HUMANIDADES', 'AREA_DESTINO_INGENIERIA', 'AREA_DESTINO_SALUD', 
                    //         'AREA_DESTINO_SERVICIOS', 'PONDERACION_NEM', 'PONDERACION_RANKING', 
                    //         'PONDERACION_C_LECTORA', 'PONDERACION_MATEMATICAS', 'PONDERACION_MATEMATICAS_2', 
                    //         'PONDERACION_HISTORIA', 'PONDERACION_CIENCIAS', 'PONDERACION_OTROS', 
                    //         'VACANTES_PRIMER_SEMESTRE', 'VACANTES_SEGUNDO_SEMESTRE', 'VACANTES_PACE', 
                    //         'MALLA_CURRICULAR', 'PERFIL_EGRESO', 'TEXTO_REQUISITO_INGRESO', 'OTROS_REQUISITOS', 
                    //         'MAIL_DIFUSION_CARRERA', 'VIGENCIA_CARRERA'
                    //     ];
                    // } else if (subproceso.value === 2) {
                    //      requiredHeaders = ['CODIGO_IES_NUM', 'COD_SEDE', 'NOMBRE_SEDE', 'COD_CARRERA', 'NOMBRE_CARRERA', 
                    //         'MODALIDAD', 'COD_JORNADA', 'VERSION', 'COD_TIPO_PLAN_CARRERA', 'CARACTERISTICAS_TIPO_PLAN', 
                    //         'DURACION_ESTUDIOS', 'DURACION_TITULACION', 'DURACION_TOTAL', 'REGIMEN', 
                    //         'DURACION_REGIMEN', 'NOMBRE_TITULO', 'NOMBRE_GRADO', 'COD_NIVEL_GLOBAL', 
                    //         'COD_NIVEL_CARRERA', 'COD_DEMRE', 'ANIO_INICIO', 'ACREDITACION', 
                    //         'ELEGIBLE_BECA_PEDAGOGIA', 'PED_MED_ODONT_OTRO', 'REQUISITO_INGRESO', 
                    //         'SEMESTRES_RECONOCIDOS', 'AREA_ACTUAL', 'AREA_DESTINO_AGRICULTURA', 
                    //         'AREA_DESTINO_CIENCIAS', 'AREA_DESTINO_CS_SOCIALES', 'AREA_DESTINO_EDUCACION', 
                    //         'AREA_DESTINO_HUMANIDADES', 'AREA_DESTINO_INGENIERIA', 'AREA_DESTINO_SALUD', 
                    //         'AREA_DESTINO_SERVICIOS', 'PONDERACION_NEM', 'PONDERACION_RANKING', 
                    //         'PONDERACION_C_LECTORA', 'PONDERACION_MATEMATICAS', 'PONDERACION_MATEMATICAS_2', 
                    //         'PONDERACION_HISTORIA', 'PONDERACION_CIENCIAS', 'PONDERACION_OTROS', 
                    //         'VACANTES_PRIMER_SEMESTRE', 'VACANTES_SEGUNDO_SEMESTRE', 'VACANTES_PACE', 
                    //         'MALLA_CURRICULAR', 'PERFIL_EGRESO', 'TEXTO_REQUISITO_INGRESO', 
                    //         'OTROS_REQUISITOS', 'MAIL_DIFUSION_CARRERA', 'CANTIDAD_MATRICULA_DFE', 
                    //         'CANTIDAD_BENEFICIO_DFE', 'VIGENCIA_CARRERA'];
                    // }
                    //     console.log('requiredHeaders: ', requiredHeaders);
                    //     // Check if file has headers
                    //     const firstLine = fileContent.split('\n')[0];
                    //     const hasHeaders = firstLine.includes(requiredHeaders[0]);
                        
                    //     if (!hasHeaders) {
                    //         // Add headers if they don't exist

                    //     rawCsvContent.value = requiredHeaders.join(';') + '\n' + fileContent;
                    //     console.log();
                    //     // If there's no header row, print the raw CSV content (for debugging)
                    //     console.log('Raw CSV content (no headers):', rawCsvContent.value);
                    // } else {
                    //     rawCsvContent.value = fileContent;
                    // }
                        // Validate CSV structure - check for headers
                        const lines = fileContent.split('\n');
                        if (lines.length < 2) {
                            throw new Error('El archivo debe contener al menos una fila de encabezados y una fila de datos');
                        }

                        // Check if first line looks like headers (contains expected CSV structure)
                        const firstLine = lines[0].trim();
                        if (!firstLine || firstLine.split(';').length < 3) {
                            throw new Error('El archivo no tiene el formato CSV correcto. Debe usar punto y coma (;) como separador y contener encabezados');
                        }

                        // Parse the CSV content using PapaParse
                        Papa.parse(rawCsvContent.value, {
                            header: true,
                            encoding: "windows-1252",
                            skipEmptyLines: true,
                            dynamicTyping: true,
                            delimiter: ";",
                            complete: (results) => {
                                try {
                                    // Validate parsing results
                                    if (!results.data || results.data.length === 0) {
                                        throw new Error('El archivo no contiene datos válidos');
                                    }

                                    // Check for parsing errors
                                    if (results.errors && results.errors.length > 0) {
                                        console.warn('CSV parsing warnings:', results.errors);
                                        // Only throw error for critical parsing issues
                                        const criticalErrors = results.errors.filter(error =>
                                            error.type === 'Delimiter' || error.type === 'Quotes'
                                        );
                                        if (criticalErrors.length > 0) {
                                            throw new Error('Error en el formato del archivo CSV. Verifique que use punto y coma (;) como separador');
                                        }
                                    }

                                    parsedData.value = results.data;
                                    console.log('Parsed data:', parsedData.value);

                                    // Validate that we have actual data rows
                                    const validRows = results.data.filter(row => {
                                        return Object.values(row).some(value =>
                                            value !== null && value !== undefined && value !== ''
                                        );
                                    });

                                    if (validRows.length === 0) {
                                        throw new Error('El archivo no contiene filas de datos válidas');
                                    }

                                    // Success message
                                    toast.add({
                                        severity: 'success',
                                        summary: 'Éxito',
                                        group: 'bl',
                                        detail: `Archivo cargado correctamente. ${validRows.length} filas de datos encontradas.`,
                                        life: 5000
                                    });

                                } catch (parseError) {
                                    console.error("Error processing parsed data:", parseError);
                                    cargarDatosHasError.value = true;
                                    cargarDatosisCorrect.value = false;

                                    // Clear the file from the upload list
                                    if (fileUploadRef.value) {
                                        fileUploadRef.value.clear();
                                    }

                                    toast.add({
                                        severity: 'error',
                                        summary: 'Error',
                                        group: 'bl',
                                        detail: parseError.message || 'Error al procesar los datos del archivo',
                                        life: 5000
                                    });
                                }
                            },
                            error: (parseError) => {
                                console.error("Error parsing CSV:", parseError);
                                cargarDatosHasError.value = true;
                                cargarDatosisCorrect.value = false;

                                // Clear the file from the upload list
                                if (fileUploadRef.value) {
                                    fileUploadRef.value.clear();
                                }

                                toast.add({
                                    severity: 'error',
                                    summary: 'Error',
                                    group: 'bl',
                                    detail: 'Error al analizar el archivo CSV. Verifique el formato del archivo.',
                                    life: 5000
                                });
                            }
                        });

                    } catch (contentError) {
                        console.error("Error processing file content:", contentError);
                        cargarDatosHasError.value = true;
                        cargarDatosisCorrect.value = false;

                        // Clear the file from the upload list
                        if (fileUploadRef.value) {
                            fileUploadRef.value.clear();
                        }

                        toast.add({
                            severity: 'error',
                            summary: 'Error',
                            group: 'bl',
                            detail: contentError.message || 'Error al procesar el contenido del archivo',
                            life: 5000
                        });
                    }
                };

            } catch (error) {
                console.error("Error handling file selection:", error);
                cargarDatosHasError.value = true;
                cargarDatosisCorrect.value = false;

                // Clear the file from the upload list
                if (fileUploadRef.value) {
                    fileUploadRef.value.clear();
                }

                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    group: 'bl',
                    detail: error.message || 'Error al procesar el archivo seleccionado',
                    life: 5000
                });
            } finally {
                cargarDatosIsLoading.value = false;
            }
        };

        // Helper functions for dynamic component
        const getInsumosData = (etapaNumber) => {
            return insumos_data.value.filter(item => item.etapa === etapaNumber);
        };

        const getCargaData = (etapaNumber) => {

            return carga_data.value.filter(item => item.etapa === etapaNumber && item.tipo === TIPO_CARGA);

        };

    const insumosWithTotals = computed(() => {
        const insumos = getInsumosData(1);
        if (insumos.length === 0) return [];

        const totalRow = {
            categoria: 'Total',
            carrerasCount: insumos.reduce((sum, item) => sum + Number(item.carrerasCount || 0), 0),
            isTotal: true
        };
        console.log('insumosWithTotals: ', totalRow);
        return [...insumos, totalRow];
    });

    const cargaWithTotals = computed(() => {
        const carga = getCargaData(1);
        if (carga.length === 0) return [];

        const totalRow = {
            categoria: 'Total',
            carrerasCount: carga.reduce((sum, item) => sum + Number(item.carrerasCount || 0), 0),
            subido_por: null,
            isTotal: true
        };
        console.log('cargaWithTotals: ', totalRow);
        return [...carga, totalRow];
    });

    const cargaEtapa2WithTotals = computed(() => {
        const carga = getCargaData(2);
        if (carga.length === 0) return [];

        const totalRow = {
            categoria: 'Total',
            carrerasCount: carga.reduce((sum, item) => sum + Number(item.carrerasCount || 0), 0),
            subido_por: null,
            isTotal: true
        };
        return [...carga, totalRow];
    });

    const cargaEtapa3WithTotals = computed(() => {
        const carga = getCargaData(3);
        if (carga.length === 0) return [];

        const totalRow = {
            categoria: 'Total',
            carrerasCount: carga.reduce((sum, item) => sum + Number(item.carrerasCount || 0), 0),
            subido_por: null,
            isTotal: true
        };
        return [...carga, totalRow];
    });


        
        const handleValidate = (etapaNumber) => {
            dialogConfirmarMU_post.value = true;
            currentEtapaForDialog.value = etapaNumber;
        };

        const handleUpload = (etapaNumber, tipo,categoria=null) => {
            dialogCargarDatosisVisible.value = true;
            currentEtapaForDialog.value = etapaNumber;
            currentTipoForDialog.value = tipo;
            currentCategoriaForDialog.value = categoria;

            // Check if the etapa already exists
            
            if (categoria === 'Total') {
            isExistingEtapa.value = true;
            }else{
            checkEtapaExists(etapaNumber, tipo,categoria);
            }
        };

        // Function to check if an etapa already exists
        

        const checkEtapaExists = (etapaNumber, tipo) => {
            const standardizedTipo = tipo === 'Insumos' ? TIPO_INSUMO : TIPO_CARGA;
            const etapaExists = oa_data.value.some(
                item => item.etapa === etapaNumber && item.tipo === standardizedTipo
            );
            
            // Set a flag to indicate if we're uploading to an existing etapa
            isExistingEtapa.value = etapaExists;
        
        };

        const handleDelete = (etapaNumber, tipo,categoria) => {
            // Find the etapa_id based on the etapa number
            
                currentEtapaForDialog.value = etapaNumber; // Use etapa_id instead of etapa number
                currentTipoForDialog.value = tipo;
                currentCategoriaForDialog.value = categoria;
                dialogVisibleEliminar.value = true;
        };

        const onUpload = async () => {
            try {
                cargarDatosIsLoading.value = true;
                cargarDatosHasError.value = false;
                cargarDatosisCorrect.value = false;

                // Validate that we have parsed data
                if (!parsedData.value || parsedData.value.length === 0) {
                    throw new Error('No hay datos para cargar. Por favor, seleccione un archivo válido primero.');
                }

                // Validate required dialog values
                if (!currentEtapaForDialog.value || !currentTipoForDialog.value) {
                    throw new Error('Información de etapa incompleta. Por favor, intente nuevamente.');
                }

                const standardizedTipo = currentTipoForDialog.value === 'Insumos' ? TIPO_INSUMO : TIPO_CARGA;

                // Validate subproceso_id
                if (!subproceso_id.value) {
                    throw new Error('ID de subproceso no válido');
                }

                // Prepare request data based on etapa type
                const requestData = {
                    csvData: parsedData.value,
                    subproceso_id: subproceso_id.value,
                    etapa_number: currentEtapaForDialog.value,
                    tipo: standardizedTipo,
                    categoria: currentCategoriaForDialog.value
                };

                // Validate request data
                if (!requestData.csvData || !Array.isArray(requestData.csvData)) {
                    throw new Error('Datos CSV no válidos');
                }

                let response;

                // Get the etapa_id from the API response or use an existing one
                if (!(currentCategoriaForDialog.value === 'Total')) {
                let etapa_id;
                
                    // If we're using an existing etapa, find its ID from the current data
                    const existingEtapa = oa_data.value.find(
                        item => item.etapa === currentEtapaForDialog.value &&
                              item.tipo === (currentTipoForDialog.value === 'Insumos' ? TIPO_INSUMO : TIPO_CARGA)
                            && item.categoria === currentCategoriaForDialog.value
                    );
                    if (existingEtapa) {
                        etapa_id = existingEtapa.id;
                    } else {
                        throw new Error("Could not find the existing etapa");
                    }
                // If categoria is 'Total', skip deletion and uploading (no action needed)

                
                // Delete existing OAs inside the etapa before uploading new data
                const deleteResponse = await axios.delete(
                    `${API_BASE_URL}oa_oferta_academica/etapa/${etapa_id}`,
                    {
                        headers: {
                            Authorization: `Bearer ${userToken.value}`,
                        },
                    }
                );
                
                if (deleteResponse.status !== 200) {
                    throw new Error("Failed to delete existing OAs inside the etapa");
                    }
                    // Now use the correct endpoint with the etapa_id
                    console.log("Uploading data to etapa_id:", etapa_id);
                    console.log("Request data:", requestData);
                    try {
                        response = await axios.post(
                        `${API_BASE_URL}oa_oferta_academica/import_csv/${etapa_id}`,
                        requestData,
                        {
                            headers: {
                                Authorization: `Bearer ${userToken.value}`,
                                "Content-Type": "application/json",
                            },
                        }
                    );
                    } catch (error) {
                        console.error("Error uploading data:", error.response?.data || error.message);

                        // Set error states
                        cargarDatosHasError.value = true;
                        cargarDatosisCorrect.value = false;

                        // Handle different types of errors
                        let errorMessage = "Ocurrió un error al cargar los datos.";
                        let errorDetails = null;

                        if (error.response) {
                            // Server responded with error status
                            if (error.response.status === 400) {
                                errorMessage = "Datos inválidos en el archivo.";
                            } else if (error.response.status === 401) {
                                errorMessage = "No tiene permisos para realizar esta acción.";
                            } else if (error.response.status === 413) {
                                errorMessage = "El archivo es demasiado grande.";
                            } else if (error.response.status === 422) {
                                errorMessage = "Error de validación en los datos.";
                            } else if (error.response.status >= 500) {
                                errorMessage = "Error interno del servidor. Intente más tarde.";
                            }

                            // Extract detailed error message if available
                            if (error.response.data?.message) {
                                errorMessage = error.response.data.message;
                            }

                            // Extract validation errors if available
                            if (error.response.data?.errors) {
                                errorDetails = error.response.data.errors;
                                errorData.value = errorDetails;
                                generateErrorCSV(errorDetails);
                            }
                        } else if (error.request) {
                            // Network error
                            errorMessage = "Error de conexión. Verifique su conexión a internet.";
                        } else {
                            // Other error
                            errorMessage = error.message || errorMessage;
                        }

                        toast.add({
                            severity: "error",
                            summary: "Error",
                            group: "bl",
                            detail: errorMessage,
                            life: 5000,
                        });

                        // Don't re-throw the error to allow the page to continue functioning
                        return; // Exit the function gracefully
                    }
            }else{

                if (currentCategoriaForDialog.value === 'Total') {
                    // Call a different endpoint for 'Total' category
                    try {
                        response = await axios.post(
                            `${API_BASE_URL}oa_oferta_academica/import_csv_total`,
                            requestData,
                            {
                                headers: {
                                    Authorization: `Bearer ${userToken.value}`,
                                    "Content-Type": "application/json",
                                },
                            }
                        );
                    } catch (error) {
                        console.error("Error uploading data (Total):", error.response?.data || error.message);

                        // Set error states
                        cargarDatosHasError.value = true;
                        cargarDatosisCorrect.value = false;

                        // Handle different types of errors for Total upload
                        let errorMessage = "Ocurrió un error al cargar los datos totales.";

                        if (error.response) {
                            if (error.response.status === 400) {
                                errorMessage = "Datos inválidos en el archivo total.";
                            } else if (error.response.status === 401) {
                                errorMessage = "No tiene permisos para cargar datos totales.";
                            } else if (error.response.status === 422) {
                                errorMessage = "Error de validación en los datos totales.";
                            } else if (error.response.status >= 500) {
                                errorMessage = "Error interno del servidor. Intente más tarde.";
                            }

                            if (error.response.data?.message) {
                                errorMessage = error.response.data.message;
                            }

                            if (error.response.data?.errors) {
                                errorData.value = error.response.data.errors;
                                generateErrorCSV(error.response.data.errors);
                            }
                        } else if (error.request) {
                            errorMessage = "Error de conexión al cargar datos totales.";
                        }

                        toast.add({
                            severity: "error",
                            summary: "Error",
                            group: "bl",
                            detail: errorMessage,
                            life: 5000,
                        });

                        // Don't re-throw the error to allow the page to continue functioning
                        return; // Exit the function gracefully
                    }
                }
            }
                
                // Check if we have a response (might be undefined if there was an error)
                if (response && response.status === 201) {
                    cargarDatosisCorrect.value = true;
                    cargarDatosHasError.value = false;

                    toast.add({
                        severity: "success",
                        summary: "Éxito",
                        group: "bl",
                        detail: `${currentTipoForDialog.value} cargados correctamente.`,
                        life: 5000,
                    });

                    // Close dialog and refresh data
                    dialogCargarDatosisVisible.value = false;

                    try {
                        await getEtapas();
                    } catch (refreshError) {
                        console.error("Error refreshing data:", refreshError);
                        toast.add({
                            severity: "warn",
                            summary: "Advertencia",
                            group: "bl",
                            detail: "Datos cargados correctamente, pero hubo un error al actualizar la vista.",
                            life: 5000,
                        });
                    }
                } else if (response) {
                    // Unexpected response status
                    cargarDatosHasError.value = true;
                    cargarDatosisCorrect.value = false;

                    toast.add({
                        severity: "error",
                        summary: "Error",
                        group: "bl",
                        detail: `Error inesperado del servidor (código: ${response.status}).`,
                        life: 5000,
                    });
                }
                // If response is undefined, errors were already handled in the catch blocks above

            } catch (error) {
                console.error("Error handling file upload:", error);

                // Set error states
                cargarDatosHasError.value = true;
                cargarDatosisCorrect.value = false;

                // Handle different types of top-level errors
                let errorMessage = "Ocurrió un error al procesar la solicitud.";

                if (error.message) {
                    errorMessage = error.message;
                } else if (typeof error === 'string') {
                    errorMessage = error;
                }

                toast.add({
                    severity: "error",
                    summary: "Error",
                    group: "bl",
                    detail: errorMessage,
                    life: 5000,
                });
            } finally {
                cargarDatosIsLoading.value = false;
            }
        }

        const validateOAEtapa = async (etapaNumber) => {
            try {
                dialogIsLoading.value = true;

                // Find all etapas within the same stage (etapaNumber) that need to be validated
                const etapasToValidate = oa_data.value.filter(item => item.etapa === etapaNumber);

                if (etapasToValidate.length === 0) {
                    throw new Error(`No etapas found for stage ${etapaNumber}.`);
                }

                // Validate all etapas within the stage
                const validationPromises = etapasToValidate.map(etapa =>
                    axios.patch(
                        `${API_BASE_URL}oa_etapa/validate/${etapa.id}`,
                        {},
                        {
                            headers: {
                                Authorization: `Bearer ${userToken.value}`,
                            },
                        }
                    )
                );

                const responses = await Promise.all(validationPromises);

                // Check if all validations were successful
                const allSuccessful = responses.every(response => response.status === 200);

                if (allSuccessful) {
                    toast.add({
                        severity: "success",
                        summary: "Éxito",
                        group: "bl",
                        detail: `Todas las etapas del stage ${etapaNumber} validadas correctamente.`,
                        life: 5000,
                    });
                    await getEtapas();
                } else {
                    toast.add({
                        severity: "error",
                        summary: "Error",
                        group: "bl",
                        detail: "Ocurrió un error al validar algunas etapas.",
                        life: 5000,
                    });
                }
            } catch (error) {
                console.error("Error validating etapas:", error);
                toast.add({
                    severity: "error",
                    summary: "Error",
                    group: "bl",
                    detail: "Ocurrió un error al validar las etapas.",
                    life: 5000,
                });
            } finally {

                resetForm();
                dialogIsLoading.value = false;
            }
        };


                const generateErrorCSV = async (errorDataAxios) => {
            try {

                // Store the CSV content for download
                errorCSVContent.value = errorDataAxios;
            } catch (error) {
                console.error('Error generating CSV:', error);
                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    group: 'bl',
                    detail: 'Error al generar el archivo de errores',
                    life: 5000
                });
            }
        }

        // Function to download the generated CSV file using Windows-1252 encoding
        const downloadErrors = () => {
            try {
                if (!errorCSVContent.value) {
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        group: 'bl',
                        detail: 'No hay errores para descargar',
                        life: 5000
                    });
                    return;
                }

                // Create a Blob with the CSV content
                const blob = new Blob([errorCSVContent.value], { type: 'text/csv;charset=windows-1252;' });
                console.log('errorCSVContent: ');
                console.log(errorCSVContent.value);
                errorCSVContent.value
                // Create a download link
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);

                // Set link properties
                link.setAttribute('href', url);
                link.setAttribute('download', `errores_oa_${anio_proceso.value}_${new Date().toISOString().slice(0, 10)}.csv`);
                link.style.visibility = 'hidden';

                // Add to document, click and remove
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                console.error('Error downloading CSV:', error);
                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    group: 'bl',
                    detail: 'Error al descargar el archivo de errores',
                    life: 5000
                });
            }
        }
        
        async function handleExport(etapaNumber, tipo, categoria=null) {
            try {
                // Validate input parameters
                if (!etapaNumber || !tipo) {
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        group: 'bl',
                        detail: 'Parámetros de exportación inválidos',
                        life: 5000
                    });
                    return;
                }

                // Find the etapa_id based on the etapa number
                const etapa = oa_data.value.find(item =>
                    item.etapa === etapaNumber &&
                    item.tipo === (tipo === 'Insumos' ? TIPO_INSUMO : TIPO_CARGA) &&
                    item.categoria === categoria
                );
                console.log('etapa found:', etapa);

                let response;

                if (etapa) {
                    currentEtapaForDialog.value = etapa.id;
                    currentTipoForDialog.value = tipo;
                    currentCategoriaForDialog.value = categoria;

                    try {
                        response = await axios.get(API_BASE_URL + `oa_oferta_academica/etapa/${currentEtapaForDialog.value}/csv`, {
                            headers: {
                                Authorization: `Bearer ${userToken.value}`
                            },
                            responseType: 'blob',
                            timeout: 30000 // 30 second timeout
                        });
                    } catch (error) {
                        console.error("Error fetching etapa data:", error);

                        let errorMessage = "Error al exportar los datos";
                        if (error.response) {
                            if (error.response.status === 404) {
                                errorMessage = "No se encontraron datos para exportar";
                            } else if (error.response.status === 401) {
                                errorMessage = "No tiene permisos para exportar estos datos";
                            } else if (error.response.status >= 500) {
                                errorMessage = "Error del servidor al generar la exportación";
                            }
                        } else if (error.code === 'ECONNABORTED') {
                            errorMessage = "Tiempo de espera agotado. Intente nuevamente";
                        } else if (error.request) {
                            errorMessage = "Error de conexión al exportar";
                        }

                        toast.add({
                            severity: 'error',
                            summary: 'Error',
                            group: 'bl',
                            detail: errorMessage,
                            life: 5000
                        });
                        return;
                    }
                } else {
                    // Check if it's a totals export first
                    if (categoria === 'Total') {
                        try {
                            currentEtapaForDialog.value = etapaNumber;
                            currentTipoForDialog.value = tipo;
                            currentCategoriaForDialog.value = categoria;

                            response = await axios.get(API_BASE_URL + `oa_oferta_academica/etapa/totals/${etapaNumber}/csv`, {
                                params: {
                                    etapa: currentEtapaForDialog.value,
                                    tipo: currentTipoForDialog.value === 'Insumos' ? TIPO_INSUMO : TIPO_CARGA,
                                    categoria: currentCategoriaForDialog.value,
                                    subproceso_id: subproceso_id.value
                                },
                                headers: {
                                    Authorization: `Bearer ${userToken.value}`
                                },
                                responseType: 'blob',
                                timeout: 30000
                            });
                        } catch (error) {
                            console.error(`Error fetching totals for etapa ${etapaNumber}:`, error);

                            let errorMessage = "Error al exportar datos totales";
                            if (error.response?.status === 404) {
                                errorMessage = "No se encontraron datos totales para exportar";
                            } else if (error.response?.status >= 500) {
                                errorMessage = "Error del servidor al generar exportación total";
                            } else if (error.code === 'ECONNABORTED') {
                                errorMessage = "Tiempo de espera agotado para exportación total";
                            }

                            toast.add({
                                severity: 'error',
                                summary: 'Error',
                                group: 'bl',
                                detail: errorMessage,
                                life: 5000
                            });
                            return;
                        }
                    } else {
                        toast.add({
                            severity: 'error',
                            summary: 'Error',
                            group: 'bl',
                            detail: `No se encontró la etapa ${etapaNumber} para exportar`,
                            life: 5000
                        });
                        return;
                    }
                }

                // Validate response
                if (!response || !response.data) {
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        group: 'bl',
                        detail: 'No se recibieron datos para exportar',
                        life: 5000
                    });
                    return;
                }

                console.log('Export response received:', response.data);
                const blob = response.data;

                // Validate blob
                if (!(blob instanceof Blob) || blob.size === 0) {
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        group: 'bl',
                        detail: 'El archivo exportado está vacío o es inválido',
                        life: 5000
                    });
                    return;
                }

                // Trigger download
                try {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');

                    a.href = url;
                    a.download = `oferta_academica_${anio_proceso.value}_subproceso_${subproceso.value}_etapa_${etapaNumber}_tipo_${tipo}_PIU.csv`;
                    a.style.display = 'none';

                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    toast.add({
                        severity: 'success',
                        summary: 'Éxito',
                        group: 'bl',
                        detail: 'Datos exportados correctamente',
                        life: 5000
                    });
                } catch (downloadError) {
                    console.error('Error during download:', downloadError);
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        group: 'bl',
                        detail: 'Error al descargar el archivo exportado',
                        life: 5000
                    });
                }

            } catch (error) {
                console.error("Error in handleExport:", error);
                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    group: 'bl',
                    detail: 'Error general al exportar los datos',
                    life: 5000
                });
            }
        }

        return {
            getDisabledEtapas,
            subproceso,
            fecha_termino,
            fecha_inicio,
            fecha_final,
            isLoading,
            globalLoading,
            anio_proceso,
            etapa_actual,
            hasAccess,
            goBack,
            oa_data,
            carga_data,
            insumos_data,
            etapa2_insumos_data,
            etapa2_carga_data,
            dialogVisibleEliminar,
            dialogIsLoading,
            deletePreConfirmation,
            muPregradoisCorrect,
            resetForm,
            validatePageState,
            validarMatriculaUnificada,
            eliminarOfertasAcademicas,
            dialogConfirmarMU_post,
            validatePreConfirmation,
            errorMessage,
            pending_text,
            finalized_text,
            dialogCargarDatosisVisible,
            cargarDatosIsLoading,
            onSelect,
            fileUploadRef,
            onUpload,
            currentEtapaForDialog,
            subproceso_id,
            currentTipoForDialog,
            currentCategoriaForDialog,
            isExistingEtapa,
            rawCsvContent,
            dialogHeader,
            // Dynamic component related
            etapas,
            getInsumosData,
            insumosWithTotals,
            getCargaData,
            cargaWithTotals,
            cargaEtapa2WithTotals,
            cargaEtapa3WithTotals,
            handleValidate,
            handleUpload,
            handleDelete,
            validateOAEtapa,
            handleExport,
            downloadErrors,
            generateErrorCSV,
            cargarDatosisCorrect,
            cargarDatosHasError,
            errorData,
            errorCSVContent,
        }
    }
}

</script>
<style scoped>
:deep() .center-header {
    text-align: center !important;
    align-content: center !important;
}

:deep() .p-datatable .p-column-header-content {
    display: block !important;

}

.yellow-icon .pi {
    color: yellow;
}

.green-icon .pi {
    color: greenyellow;
}

:deep(.p-fileupload-file-thumbnail) {
    display: none !important;
}
</style>